import { ChannelTypes } from './../../models/ChannelType';
import { ChannelTypesToString } from 'src/app/models/ChannelType';
import { ChangeGroupChannelQuestionComponent } from './../editor/popups/change-group-channel-question/change-group-channel-question.component';
import { Component, OnInit, EventEmitter, Inject, ViewChild, ElementRef, OnDestroy } from '@angular/core';
import { Location, DOCUMENT } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { ModalService } from '../../services/Tools/ModalService';
import { ServerService } from '../../services/server.service';
import { FlowDefinition } from '../../models/FlowDefinition';
import { StatusResponse } from '../../models/StatusResponse';
import { NewFlowComponent } from "../editor/popups/new-flow/new-flow.component";
import { ErrorPopupComponent } from "../error-popup/error-popup.component";
import { finalize } from 'rxjs/operators'
import { EditorService } from '../../services/editor.service';
import { DeleteInput } from '../../models/UI/DeleteInput';
import { DeleteGroupQuestionComponent } from '../editor/popups/delete-group-question/delete-group-question.component';
import { getBaseUrl, getTokenPayload, ySmartEnabled, ySocialUrl } from '../../Utils/window';
import { environment } from '../../../environments/environment';
import { TranslateService } from '@ngx-translate/core';
import { HttpResponse } from "@angular/common/http";
import { TypedJSON, jsonArrayMember } from "typedjson";
import { ChatDefinition } from "../../models/ChatDefinition";
import { FilterComponent } from '../filter/filter.component';
import { FlowTypes } from 'src/app/models/FlowType';
import { ModuleDefinition } from 'src/app/models/ModuleDefinition';
import { Permission } from 'src/app/models/Permission';
import { v4 as uuidv4 } from 'uuid';
import { ArrayUtils } from 'src/app/Utils/ArrayUtils';

@Component({
  selector: 'app-dashboard-module',
  templateUrl: './dashboard-module.component.html',
  styleUrls: ['./dashboard-module.component.scss'],
  host: { 'class': 'container-fluid-no-padding' }
})
export class DashboardModuleComponent implements OnInit, OnDestroy {
  @ViewChild('fileField', { static: false }) fileField: ElementRef;

  constructor(
    public modalService: ModalService,
    private serverService: ServerService,
    private editorService: EditorService,
    private routerService: Router,
    private location: Location,
    private translate: TranslateService,
    private router: Router,
    private route: ActivatedRoute,
    @Inject(DOCUMENT) private doc: any) {
  }

  isAdmin: boolean = false;
  canEdit: boolean = true;
  canPublish: boolean = true;
  canSeeStatistics: boolean = true;
  canAccessYSmart: boolean = true;
  loading: boolean = true;
  flowsLoaded: boolean = false;
  flows: FlowDefinition[];
  masterFlows: FlowDefinition[] = [];
  moduleFlows: FlowDefinition[] = [];
  readOnlyModuleFlows: FlowDefinition[] = [];
  keepSessionInterval: number;
  paramsSub: any;
  homeUrl: string;
  settingsUrl: string;
  logsErrorUrl: string;
  globalStatisticUrl: string;
  companyName: string = '';
  cachedKw: string = undefined;
  cachedChannel: string = undefined;
  keywordfilterTag: string;
  channelFilterTag: string;
  isProd: boolean = false;

  newFlow() {
    let createAction = new EventEmitter<any>();
    createAction.subscribe((info: any) => {
      this.onCreate(info.name, info.channel, info.flowType);
    });

    this.modalService.init(NewFlowComponent, {}, { CreateAction: createAction });
  }

  openFilter() {
    let createAction = new EventEmitter<any>();
    createAction.subscribe((info: any) => {
      this.onFilter(info.name, info.channel);
    });
    this.modalService.init(FilterComponent, { flowName: this.cachedKw, selectedChannel: this.cachedChannel }, { FilterAction: createAction });
  }

  onFilter(kw, channel) {
    this.loading = true;
    this.cachedKw = kw;
    this.cachedChannel = channel;
    console.log(`Filtrando bajo keyword=${kw} del canal=${channel}`);
    this.keywordfilterTag = kw;
    this.channelFilterTag = channel;
    this.filterFlows();
    setTimeout(_ => { this.loading = false }, 300);
  }

  private filterFlows() {
    this.masterFlows = this.flows.filter(f => {
      let validkw, validchannel;
      validkw = this.isKwValid(this.keywordfilterTag, f.name);
      validchannel = this.isChannelValid(this.channelFilterTag, f.channel);
      return validchannel && validkw && f.ActiveProductionVersion != null;
    });

    this.moduleFlows = this.flows.filter((f) => {
      let validkw, validchannel;
      validkw = this.isKwValid(this.keywordfilterTag, f.name);
      validchannel = this.isChannelValid(this.channelFilterTag, f.channel);
      return validkw && validchannel && f.ActiveStagingVersion != null;
    });
  }

  removeFilterKeyword() {
    this.keywordfilterTag = undefined;
    this.onFilter(undefined, this.channelFilterTag);
  }

  removeFilterChannel() {
    this.channelFilterTag = undefined;
    this.onFilter(this.keywordfilterTag, undefined);
  }

  private isKwValid(kw: string, name: string): boolean {
    if (kw == undefined) {
      return true;
    } else {
      return name.normalize("NFD").replace(/[\u0300-\u036f]/g, "").toLowerCase().includes(
        kw.normalize("NFD").replace(/[\u0300-\u036f]/g, "").toLowerCase()
      );
    }
  }

  private isChannelValid(ch: string, channel: string) {
    if (ch == undefined) {
      return true;
    } else {
      return (channel == ch);
    }
  }

  onCreate(flowName, channel, flowType) {
    console.log(`Creando un nuevo flow=${flowName} para el canal=${channel}`);
    this.loading = true;
    var blob = this.editorService.createNewFromTemplate(flowName, channel);

    this.serverService.createFlow(flowName, channel, blob, flowType, null)
      .pipe(finalize(() => {
        this.loading = false;
      }))
      .subscribe((status: StatusResponse) => {
        this.modalService.destroy();

        let permissions = [];
        let { uid } = getTokenPayload();
        if (typeof (status.data.permissions) !== 'undefined' && status.data.permissions.length > 0) {
          permissions.push(status.data.permissions.find(p => p.userId === uid));
        }

        this.editorService.setCurrentFlowId(status.data.id as number, false, channel, flowName, flowType, permissions);

        if (this.companyName.length > 0) {
          this.routerService.navigateByUrl(`${this.companyName}/edit-flow/${status.data.id}/false`);
        }
        else {
          this.routerService.navigateByUrl(`edit-flow/${status.data.id}/false`);
        }
      },
        error => {
          var inputs: any = {};
          if (error.error != null) {
            inputs.Title = 'ERROR';
            inputs.Desc = error.error.message;
          }
          this.modalService.init(ErrorPopupComponent, inputs, {});
        });
  }

  onClone(flow: FlowDefinition) {
    this.loading = true;
    this.serverService.duplicateFlow(flow.id, flow.name + " copia")
      .pipe(finalize(() => {
        this.loading = false;
      }))
      .subscribe((status: StatusResponse) => {
        this.ngOnInit();
      },
        error => {
          this.modalService.init(ErrorPopupComponent, {}, {});
        });
  }

  onUpload(flow: FlowDefinition, isPublished: boolean) {
    var vid = isPublished ? flow.ActiveProductionVersion.id : flow.ActiveStagingVersion.id;
    var vnumber = isPublished ? flow.ActiveProductionVersion.number : flow.ActiveStagingVersion.number;
    let deleteInfo = new DeleteInput();
    deleteInfo.ElementName = vnumber;

    let emmitAction = new EventEmitter();
    emmitAction.subscribe(() => {

      this.fileField.nativeElement.click();

      var self = this;
      this.fileField.nativeElement.onchange = async function (event) {
        var fileList = self.fileField.nativeElement.files;
        if (fileList.length > 0) {
          var reader = new FileReader();
          reader.onload = function () {
            var text = reader.result as string;
            var contents = null;
            var error = false;
            var errorTitle = null;
            var errorDesc = null;
            var definition = null;
            var emmitActionChannel = new EventEmitter();
            try {
              contents = JSON.parse(text);
              if (typeof (contents.channel) === 'undefined' ||
                contents.channel === null) {
                error = true;
                errorTitle = 'UPLOAD_ERROR';
                errorDesc = 'UPLOAD_ERROR_INVALIDCONTENTS';
              }
              else if ((typeof (contents.type) === 'undefined' || contents.type === null) &&
                (typeof (contents.type) !== 'undefined' && contents.type !== null)) {
                error = true;
                errorTitle = 'UPLOAD_ERROR';
                errorDesc = 'UPLOAD_ERROR_INVALIDCONTENTS';
              }
              else if (typeof (flow.type) !== 'undefined' &&
                typeof (contents.type) !== 'undefined' &&
                contents.type !== flow.type) {
                error = true;
                errorTitle = 'UPLOAD_ERROR';
                errorDesc = 'UPLOAD_ERROR_TYPEMISMATCH';
              }
              else {
                definition = JSON.stringify(contents.def);
              }
            }
            catch (e) {
              console.log(e);
              error = true;
              errorTitle = 'UPLOAD_ERROR';
              errorDesc = 'UPLOAD_ERROR_INVALIDJSON';
            }

            emmitActionChannel.subscribe((def?) => {
              self.serverService.getFlow(flow.id).subscribe(f => {
                if (def) {
                  error = false;
                  definition = def;
                }
                if (!error) {
                  let fDef = isPublished ? f.ActiveProductionVersion.blob : f.ActiveStagingVersion.blob;
                  //console.log(`[DEBUG  DashboardModule] Tipo de fDef: ${typeof fDef}, Tipo de definition: ${typeof definition}`);

                  let state: ChatDefinition = null;
                  let defState: ChatDefinition = null;
                  try {
                    // Validar y corregir estructura antes de deserializar
                    let definitionData = JSON.parse(definition);
                    if (!definitionData.BlockGroups) {
                      //console.log(`[DEBUG  DashboardModule] Inicializando BlockGroups vacío en definition`);
                      definitionData.BlockGroups = [];
                    }
                    if (!definitionData.BlockList) {
                      //console.log(`[DEBUG  DashboardModule] Inicializando BlockList vacío en definition`);
                      definitionData.BlockList = [];
                    }

                    // Manejar el caso donde fDef es undefined debido a carga en fragmentos
                    let fDefData;
                    if (fDef === undefined || fDef === null) {
                      //console.log(`[DEBUG  DashboardModule] fDef es undefined/null, usando estructura básica para flujos grandes`);
                      fDefData = { BlockGroups: [], BlockList: [] };
                    } else {
                      // Convertir fDef a string si es necesario y validar estructura
                      let fDefStr = typeof fDef === 'string' ? fDef : JSON.stringify(fDef);
                      fDefData = JSON.parse(fDefStr);
                      if (!fDefData.BlockGroups) {
                        //console.log(`[DEBUG  DashboardModule] Inicializando BlockGroups vacío en fDef`);
                        fDefData.BlockGroups = [];
                      }
                      if (!fDefData.BlockList) {
                        //console.log(`[DEBUG  DashboardModule] Inicializando BlockList vacío en fDef`);
                        fDefData.BlockList = [];
                      }
                    }

                    state = TypedJSON.parse(JSON.stringify(definitionData), ChatDefinition);
                    defState = TypedJSON.parse(JSON.stringify(fDefData), ChatDefinition);

                    state.YSocialSettings.Url = ySocialUrl();

                    state.DefaultBlocks = state.DefaultBlocks.filter(b => self.editorService.isDefaultBlockValidForChannel(b.Id, flow.channel))

                    state.BlockGroups.forEach(g => {
                      g.Blocks.forEach(b => {
                        b.Pieces = b.Pieces.filter(p => self.editorService.isPieceSupportedByChannel(p, flow.channel));
                        b.Pieces = b.Pieces.filter(p => self.editorService.isPieceSupportedByType(p, flow.type));
                      });
                    });
                    state.DefaultBlocks.forEach(b => {
                      b.Pieces = b.Pieces.filter(p => self.editorService.isPieceSupportedByChannel(p, flow.channel));
                      b.Pieces = b.Pieces.filter(p => self.editorService.isPieceSupportedByType(p, flow.type));
                    });
                    state.BlockList.forEach(b => {
                      b.Pieces = b.Pieces.filter(p => self.editorService.isPieceSupportedByChannel(p, flow.channel));
                      b.Pieces = b.Pieces.filter(p => self.editorService.isPieceSupportedByType(p, flow.type));
                    });

                    state.BlockList.forEach(block => {
                      var idOld = block.Id;
                      block.ModuleId = flow.id;
                      if (defState.BlockList.find(b => b.Id == idOld && b.ModuleId !== block.ModuleId)) { // DEJAR b.id == idOld COMO ESTA, NO AÑADIR === PORQUE SINO ROMPE
                        block.reset(flow.id);
                        //block.Name = ArrayUtils.getUniqueName(block.Name, state.BlockList, (str, instance) => { return str === instance.Name; });

                        state.BlockList.forEach(b => b.updateBlockReferences(idOld, block.Id))
                        state.DefaultBlocks.forEach(b => b.updateBlockReferences(idOld, block.Id))
                        state.CommandDefinitions.forEach(c => c.updateBlockReference(idOld, block.Id))
                        state.CognitivityDefinitions.forEach(c => {
                          c.updateBlockReference(idOld, block.Id)
                          c.updateAllBlockReferences(idOld, block.Id)
                        })
                        state.PersistentMenuEntries.forEach(pm => {
                          pm.buttons.forEach(b => b.updateBlockReferences(idOld, block.Id))
                        })
                        if (state.IceBreakers !== null) {
                          state.IceBreakers.buttons.forEach(b => b.updateBlockReferences(idOld, block.Id));
                        }
                      }

                      state.BlockGroups.forEach(g => {
                        for (let i = 0; i < g.BlockIDs.length; i++) {
                          if (g.BlockIDs[i] === idOld) {
                            g.BlockIDs[i] = block.Id;
                            g.Blocks[i].Id = block.Id;
                          }
                        }
                      });

                    });

                    state.BlockGroups.forEach(g => {
                      g.resetId();
                      g.ModuleId = flow.id;
                      //g.Name = ArrayUtils.getUniqueName(g.Name, state.BlockGroups, (str, instance) => { return str === instance.Name; });
                    });

                    state.BlockDeletedList = [];
                    state.GroupDeletedList = [];

                    // TODO: Blanquear secciones innecesarias para que pese menos (ej. cognitividad)

                    let oldDefinition = definition;
                    definition = TypedJSON.stringify(state, ChatDefinition, { preserveNull: true });

                    if (self.editorService.exceedMaxBlocks(flow, state)) {
                      error = true;
                      errorTitle = 'UPLOAD_ERROR';
                      errorDesc = 'UPLOAD_ERROR_INVALIDBLOCKS_LITE';
                    }

                    if (self.editorService.exceedMaxCommands(flow, state)) {
                      error = true;
                      errorTitle = 'UPLOAD_ERROR';
                      errorDesc = 'UPLOAD_ERROR_INVALIDCOMMANDS_LITE';
                    }

                    if (definition !== oldDefinition) {
                      console.log(`Se filtraron piezas por no ser compatibles`);
                    }

                  } catch (e) {
                    console.log(`No se pudo deserializar el JSON tipado ${contents.def}: ${e}`);
                    error = true;
                    errorTitle = 'UPLOAD_ERROR';
                    errorDesc = 'UPLOAD_ERROR_INVALIDJSON';
                  }
                }

                if (!error) {
                  self.loading = true;
                  console.log(`[IMPORTACIÓN] Iniciando importación de módulo "${flow.name}". Tamaño: ${Math.round(definition.length / 1024)} KB`);
                  self.serverService.overrideFlow(flow.id, vid, definition as string)
                    .subscribe((status: StatusResponse) => {
                      console.log(`[IMPORTACIÓN] Módulo "${flow.name}" importado correctamente`);
                      self.editorService.onFlowUploaded.emit(flow.name);
                      self.ngOnInit();
                    },
                      error => {
                        console.error(`[IMPORTACIÓN] Error al importar módulo "${flow.name}":`, error);
                        var err = null, desc = null;
                        if (error && error.status == 403) {
                          err = 'UPLOAD_ERROR';
                          desc = 'UPLOAD_ERROR_INVALIDCONTENTS'
                        }
                        self.modalService.init(
                          ErrorPopupComponent,
                          { Title: err, Desc: desc },
                          {});
                      });
                } else {
                  self.modalService.init(
                    ErrorPopupComponent,
                    { Title: errorTitle, Desc: errorDesc },
                    {});
                }
              })
            });

            if (!error) {
              if (contents.channel !== flow.channel) {
                self.changeFlowChannel(emmitActionChannel, contents);
              } else {
                emmitActionChannel.emit();
              }
            } else {
              self.modalService.init(
                ErrorPopupComponent,
                { Title: errorTitle, Desc: errorDesc },
                {});
            }

          };
          reader.readAsText(fileList[0]);
        }
      }
    });
    this.modalService.init(
      DeleteGroupQuestionComponent,
      {
        DeleteDetail: deleteInfo, Title: 'ARE_YOU_SURE_OVERRIDE_QUESTION',
        HideAffected: true, HideConfirmation: true, deleteText: 'ACCEPT'
      }, { DeleteAction: emmitAction });
  }

  changeFlowChannel(action, contents) {
    let emmitAction = new EventEmitter();
    let definition;
    emmitAction.subscribe(() => {
      definition = JSON.stringify(contents.def);
      action.emit(definition);
    });
    this.modalService.init(
      ChangeGroupChannelQuestionComponent,
      {
        Title: 'ARE_YOU_SURE_CHANGE_CHANNEL',
        deleteText: 'ACCEPT'
      }, { ChangeChannelAction: emmitAction });

  }

  onDownload(flow: FlowDefinition, isPublished: boolean) {
    this.loading = true;
    this.serverService.downloadFlow(flow.id, isPublished ? flow.ActiveProductionVersion.id : flow.ActiveStagingVersion.id)
      .pipe(finalize(() => {
        this.loading = false;
      }))
      .subscribe(async (blobResponse: any) => {
        try {
          // Si la respuesta es una promesa (debido a la descompresión), esperar a que se resuelva
          const blob = blobResponse instanceof Promise ? await blobResponse : blobResponse;

          // Doing it this way allows you to name the file
          var link = document.createElement('a');
          //link.style = "display: none";
          link.href = window.URL.createObjectURL(blob);
          link.download = `Flow ${flow.name}.json`;
          document.body.appendChild(link);
          link.click();
          setTimeout(function () {
            document.body.removeChild(link);
            window.URL.revokeObjectURL(link.href);
          }, 100);
        } catch (error) {
          console.error('Error al procesar la descarga:', error);
          var errorDesc: any = { Title: 'ERROR', Desc: 'Error al procesar la descarga del flujo' };
          this.modalService.init(ErrorPopupComponent, errorDesc, {});
        }
      },
        error => {
          console.error('Error al descargar flujo:', error);
          var errorDesc: any = { Title: 'ERROR', Desc: 'Error al descargar el flujo' };
          this.modalService.init(ErrorPopupComponent, errorDesc, {});
        })
  }

  onPublish(flow: FlowDefinition) {
    let errorInfo: any = {
      title: <string>'INVALID_CHAT_TITLE',
      desc: <string>'INVALID_CHAT_DESCRIPTION'
    };

    if (typeof (flow.ActiveStagingVersion.blob) !== 'undefined' && flow.ActiveStagingVersion.blob !== null) {
      if (!this.isFlowValid(flow, errorInfo)) {
        this.modalService.init(ErrorPopupComponent, { Title: errorInfo.title, Desc: errorInfo.desc }, {});
        return;
      }

      this.publish(flow);
    }
    else {
      this.serverService.getFlow(flow.id)
        .subscribe((downloadedFlow: FlowDefinition) => {
          flow.ActiveProductionVersion = downloadedFlow.ActiveProductionVersion;
          flow.ActiveStagingVersion = downloadedFlow.ActiveStagingVersion;

          if (!this.isFlowValid(flow, errorInfo)) {
            this.modalService.init(ErrorPopupComponent, { Title: errorInfo.title, Desc: errorInfo.desc }, {});
            return;
          }

          this.publish(flow);
        });
    }
  }

  publish(flow: FlowDefinition) {
    let deleteInfo = new DeleteInput();
    let emitAction = new EventEmitter();
    emitAction.subscribe(() => {
      this.loading = true;
      this.serverService.publishFlow(flow.id)
        .subscribe((status: StatusResponse) => {
          this.editorService.onFlowPublished.emit(flow.name);
          this.ngOnInit();
        },
          error => {
            this.loading = false;
            this.modalService.init(ErrorPopupComponent, {}, {});
          });
    });

    deleteInfo.ElementName = flow.name;
    this.modalService.init(
      DeleteGroupQuestionComponent,
      {
        DeleteDetail: deleteInfo,
        Title: 'ARE_YOU_SURE_PUBLISH_QUESTION_FLOW',
        HideAffected: true,
        HideConfirmation: true,
        deleteText: 'ACCEPT'
      },
      { DeleteAction: emitAction }
    );
  }

  isFlowValid(flow: FlowDefinition, errorInfo: any): boolean {
    //console.log(`[DEBUG  DashboardModule] Validando flujo. Tipo de blob: ${typeof flow.ActiveStagingVersion.blob}`);
    let blobStr = typeof flow.ActiveStagingVersion.blob === 'string'
      ? flow.ActiveStagingVersion.blob
      : JSON.stringify(flow.ActiveStagingVersion.blob);
    this.editorService.restoreFromSerializedString(blobStr, flow.channel);
    this.editorService.setCurrentFlow(flow, false);
    if (flow.master_flow_id === null) {
      var master = new ModuleDefinition();
      master.createMaster();
      master.id = flow.id;
      this.editorService.setCurrentModule(master);
    } else {
      var child = new ModuleDefinition();
      child.id = flow.id;
      this.editorService.setCurrentModule(child);
    }

    return this.editorService.isChatValid(errorInfo);
  }

  onDelete(flow: FlowDefinition, isProductive: boolean) {
    let deleteInfo = new DeleteInput();
    let emitAction = new EventEmitter();
    emitAction.subscribe(() => {

      this.deleteFlow(flow, isProductive);
    });
    deleteInfo.ElementName = flow.name;
    this.modalService.init(
      DeleteGroupQuestionComponent,
      { DeleteDetail: deleteInfo, Title: 'ARE_YOU_SURE_QUESTION_FLOW', HideAffected: true }, { DeleteAction: emitAction });
  }

  deleteFlow(flow: FlowDefinition, isProductive: boolean) {
    this.loading = true;
    this.serverService.deleteFlow(flow.id, isProductive)
      .pipe(finalize(() => {
        this.loading = false;
      }))
      .subscribe((status: StatusResponse) => {
        this.editorService.onFlowDeleted.emit(flow.name);
        if (flow.master_flow_id === null) {
          this.router.navigateByUrl('/home');
        }
        else {
          this.ngOnInit();
        }
      },
        error => {
          var errorDesc: any = {};
          if (error.status == 403) {
            errorDesc.Title = 'CANNOT_DELETE_FLOW_TITLE';
            errorDesc.Desc = error.error.code == 1002 ? 'CANNOT_DELETE_FLOW_DESC' : 'CANNOT_DELETE_FLOW_USED_DESC';
          }
          this.modalService.init(ErrorPopupComponent, errorDesc, {});
        });
  }

  getChannelType(channel: string): string {
    let name = ChannelTypesToString(channel);
    return name;
  }

  getChannelClass(channel: string): string {
    return FlowDefinition.getFlowClass(channel);
  }

  ngOnInit() {
    this.paramsSub = this.route.params.subscribe(params => {
      if (params['companyName']) {
        this.companyName = params['companyName'];
        this.homeUrl = `/${this.companyName}/home`;
      }
      else {
        this.companyName = '';
        this.homeUrl = `/home`;
      }

      if (params['id']) {
        this.isProd = params['isProd'] === 'true';

        let currentFlow = this.editorService.getCurrentFlow();

        if (currentFlow !== null &&
          currentFlow !== undefined &&
          currentFlow.modules !== null &&
          currentFlow.modules !== undefined &&
          (Array.isArray(currentFlow.modules) && currentFlow.modules.length > 0)) {
          let modules = currentFlow.modules;
          currentFlow.modules = [];
          if (this.isProd) {
            if (currentFlow.ActiveProductionVersion !== null) {
              this.masterFlows.push(currentFlow);
            }
            this.flows = modules.filter(f => f.ActiveStagingVersion != null);
          }
          else {
            this.masterFlows.push(currentFlow);
            this.flows = modules;
          }

          this.readOnlyModuleFlows = this.flows.filter(f => f.ActiveStagingVersion != null && !this.canAccess(f.users_permissions_flows[0]));
          this.moduleFlows = this.flows.filter(f => f.ActiveStagingVersion != null && this.canAccess(f.users_permissions_flows[0]));

          this.loading = false;
          this.flowsLoaded = true;
          return;
        }
        this.loading = true;
        this.flowsLoaded = false;


        let flowId = params['id'];
        flowId = Number(flowId);
        this.serverService.getFlows()
          .pipe(finalize(() => {
            this.loading = false;
            this.flowsLoaded = true;
          }))
          .subscribe((flows: FlowDefinition[]) => {
            if (this.isProd) {
              this.masterFlows = flows.filter(f => f.ActiveProductionVersion != null && f.id === flowId);
              this.moduleFlows = flows.filter(f => f.ActiveStagingVersion != null && f.master_flow_id === flowId);
            } else {
              const flow = flows.find(f => f.id === flowId)
              const masterFlowId = flow.master_flow_id === null ? flow.id : flow.master_flow_id

              this.masterFlows = flows.filter(f => f.id === masterFlowId);
              this.moduleFlows = flows.filter(f => f.master_flow_id === masterFlowId);
            }

            // Combinar masterFlows y moduleFlows 
            this.flows = this.masterFlows.concat(this.moduleFlows);

            this.readOnlyModuleFlows = this.moduleFlows.filter(f => !this.canAccess(f.users_permissions_flows[0]));
            this.moduleFlows = this.moduleFlows.filter(f => this.canAccess(f.users_permissions_flows[0]));
          },
            error => {
              this.modalService.init(ErrorPopupComponent, {}, {});
            });
      }
    });
  }

  canAccess(p: Permission) {
    if (p === undefined) {
      return false;
    }
    return p.canEdit || p.canPublish;
  }

  ngOnDestroy() {
    if (this.paramsSub != null) {
      this.paramsSub.unsubscribe();
    }
    clearInterval(this.keepSessionInterval);
  }

  logout() {
    this.doc.location.pathname = Location.joinWithSlash(getBaseUrl(), '/log_out_from_site');
  }
}
