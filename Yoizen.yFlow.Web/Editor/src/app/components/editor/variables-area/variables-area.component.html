<div class="variables">

  <app-search-bar
  [searchTerm]="currentSearch"
  [showCloseButton]="false"
  (onSearch)="onSearch($event)"
  [minLength]="0"
    (onCleanSearch)="onCleanSearch()">
  </app-search-bar>
  <div class="header">
    <div>{{'VARIABLE_NAME' | translate}}</div>
    <div>{{'VARIABLE_TYPE' | translate}}</div>
    <div>{{'VARIABLE_DEFAULT_VALUE' | translate}}</div>
    <div>{{'VARIABLE_CONSTANT' | translate}}</div>
    <div>{{'VARIABLE_HIDDEN' | translate}}</div>
    <div>{{'VARIABLE_PERSIST' | translate}}</div>
    <div *ngIf="GPTEnabled">{{'VARIABLE_SEND_YSMART' | translate}}</div>
    <div></div>
  </div>

  <div #variableContainer class="variable-viewport" cdkScrollable>
    <div class="variable" *ngFor="let variable of displayedVariables; trackBy: trackByVariable">
      <div class="name">
        <input class="input variable-name"
               type="text"
               [placeholder]="'VARIABLE_NAME' | translate"
               [disabled]="readOnly || (currentFlow?.type == '2' && !isMasterFlow())"
               spellcheck="false"
               autocomplete="off"
               [(ngModel)]="variable.Name"
               [ngClass]="{'invalid-input': isInvalidValidName(variable.Name, variable.Id)}" />
      </div>

      <div class="type">
        <select class="select"
                [(ngModel)]="variable.Type"
                [disabled]="readOnly || (currentFlow?.type == '2' && !isMasterFlow())">
          <option *ngFor="let type of getVariablesTypes()"
                  [value]="type.value">{{ type.localized | translate }}</option>
        </select>
      </div>

      <div class="defaultvalue">
        <ng-container *ngIf="canHaveDefaultValue(variable)">
          <input *ngIf="variable.Type !== 'Array' && variable.Type !== 'Object'"
                 class="input"
                 type="text"
                 [placeholder]="'VARIABLE_DEFAULT_VALUE' | translate"
                 [disabled]="readOnly || (currentFlow?.type == '2' && !isMasterFlow())"
                 spellcheck="false"
                 autocomplete="off"
                 [ngClass]="{'invalid-input': !isDefaultValueValid(variable)}"
                 (change)="defaultValueChanged(variable)"
                 [(ngModel)]="variable.DefaultValue" />

          <textarea *ngIf="variable.Type === 'Array' || variable.Type === 'Object'"
                    class="input"
                    rows="4"
                    [placeholder]="'VARIABLE_DEFAULT_VALUE' | translate"
                    [disabled]="readOnly || (currentFlow?.type == '2' && !isMasterFlow())"
                    spellcheck="false"
                    autocomplete="off"
                    [ngClass]="{'invalid-input': !isDefaultValueValid(variable)}"
                    (change)="defaultValueChanged(variable)"
                    [(ngModel)]="variable.DefaultValue">
          </textarea>
        </ng-container>
      </div>

      <div class="constant">
        <ui-switch *ngIf="hasDefaultValue(variable)"
                   [(ngModel)]="variable.Constant"
                   [disabled]="readOnly"
                   color="#45c195"
                   size="small"
                   defaultBgColor="#e0e0e0"
                   switchColor="#ffffff">
        </ui-switch>
      </div>

      <div class="constant">
        <ui-switch *ngIf="hasDefaultValue(variable) && variable.Constant"
                   [(ngModel)]="variable.Private"
                   [disabled]="readOnly"
                   color="#45c195"
                   size="small"
                   defaultBgColor="#e0e0e0"
                   switchColor="#ffffff">
        </ui-switch>
      </div>

      <div class="constant">
        <ui-switch *ngIf="!variable.Constant && (variable.Type === 'Array' || variable.Type === 'Object')"
                   [(ngModel)]="variable.Persist"
                   [disabled]="readOnly"
                   color="#45c195"
                   size="small"
                   defaultBgColor="#e0e0e0"
          switchColor="#ffffff">
        </ui-switch>
      </div>

      <div class="constant" *ngIf="GPTEnabled">
        <ui-switch [(ngModel)]="variable.SendYSmart"
                   [disabled]="readOnly"
                   color="#45c195"
                   size="small"
                   defaultBgColor="#e0e0e0"
                   switchColor="#ffffff">
        </ui-switch>
      </div>

      <div class="trash" *ngIf="!readOnly && (currentFlow?.type != '2' || isMasterFlow())">
        <div (click)="deleteVar(variable)"
             data-toggle="tooltip"
             ngbTooltip="{{ 'VARIABLE_REMOVE' | translate }}"
             placement="left"
             tooltipClass="tooltip-trash-left">
          <span class="fa fa fa-trash-alt"></span>
        </div>
      </div>

    </div>
  </div>

  <app-loading
    *ngIf="isLoadingMore"
    [overlay]="false"
    size="medium">
    <span>{{ 'LOADING_MORE_VARIABLES' | translate }}</span>
  </app-loading>
  <div class="actions">
    <div class="add" (click)="addVar()" *ngIf="!readOnly && (currentFlow?.type != '2' || isMasterFlow())">
      <span class="fa fa-plus"></span> {{ 'VARIABLE_ADD' | translate }}
    </div>
    <div class="import" (click)="importVariables()" *ngIf="!readOnly && (currentFlow?.type != '2' || isMasterFlow())">
      <span class="fa fa-file-import"></span> {{ 'VARIABLES_IMPORT' | translate }}
    </div>
  </div>
</div>
