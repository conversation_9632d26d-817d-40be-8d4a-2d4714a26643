import { Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, ViewChild,AfterViewInit } from '@angular/core';
import { VariableDefinition } from '../../../models/VariableDefinition';
import { EditorService } from '../../../services/editor.service';
import * as _ from 'lodash';
import { TypeDefinition } from "../../../models/TypeDefinition";
import { DeleteGroupQuestionComponent } from "../popups/delete-group-question/delete-group-question.component";
import { DeleteInput } from "../../../models/UI/DeleteInput";
import { ModalService } from "../../../services/Tools/ModalService";
import * as moment from 'moment';
import { FlowVariablesSelectorDialogComponent } from "../popups/flow-variables-selector-dialog/flow-variables-selector-dialog.component";
import { FlowDefinition } from 'src/app/models/FlowDefinition';
import { CognitivityProjectType } from 'src/app/models/cognitivity/CognitivityProject';
import { ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { Subject } from 'rxjs';
import { debounceTime, takeUntil } from 'rxjs/operators';
import { CdkScrollable } from '@angular/cdk/scrolling';


@Component({
  selector: 'app-variables-area',
  templateUrl: './variables-area.component.html',
  styleUrls: ['./variables-area.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class VariablesAreaComponent implements OnInit, OnDestroy {
  displayedVariables: VariableDefinition[] = [];
  pageSize = 50;
  currentPage = 0;
  isLoadingMore = false;
  isSearchActive = false;
  currentSearch: string = null;

  private allVariables: VariableDefinition[] = [];
  private searchDebouncer = new Subject<string>();

  private destroy$ = new Subject<void>();
  @Input() readOnly: boolean = false;
  variables: VariableDefinition[];
  currentFlow: FlowDefinition;
  GPTEnabled: boolean = false;

  @ViewChild(CdkScrollable, { static: false }) scrollable: CdkScrollable;
  @ViewChild('variableContainer', { static: false } ) variableContainer: ElementRef;

  private cachedTypes: any;
  private validationCache = new Map<string, boolean>();


  constructor(
    private editorService: EditorService,
    private modalService: ModalService,
    private cdr: ChangeDetectorRef
  ) {
    //console.time('VariablesArea-Constructor');
    //console.timeEnd('VariablesArea-Constructor');
  }

  getVariablesTypes() {
    //console.time('GetVariablesTypes');
    if (!this.cachedTypes) {
      this.cachedTypes = VariableDefinition.variableType.filter(v =>
        v.value !== TypeDefinition.Any);
    }
    //console.timeEnd('GetVariablesTypes');
    return this.cachedTypes;
  }
  private setupScrollListener() {
    if (this.scrollable) {
      this.scrollable.elementScrolled()
        .pipe(takeUntil(this.destroy$))
        .subscribe(() => {
          const element = this.scrollable.getElementRef().nativeElement;
          if (element.scrollTop + element.clientHeight >= element.scrollHeight - 100) {
            this.loadMoreVariables();
          }
        });
    }
  }
  ngOnInit() {

    this.allVariables = this.editorService.getVariableList();
    this.variables = this.allVariables.slice();
    this.currentFlow = this.editorService.getCurrentFlow();

    if (this.currentFlow.type == '2' && this.variables.length == 0) {
      this.editorService.createNewVariableWithName("Ingreso");
    }
    this.setupSearch();
    this.loadMoreVariables();

    this.editorService.onVariablesChanged
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.allVariables = this.editorService.getVariableList();
        this.variables = this.allVariables.slice();
        this.displayedVariables = [];
        this.currentPage = 0;
        this.loadMoreVariables();

        if (!this.destroy$.closed) {
          this.cdr.markForCheck();
        }
      });

    setTimeout(() => {
      this.setupScrollListener();
    });
  }
  trackByVariable(index: number, variable: VariableDefinition): number {
    return variable.Id;
  }

  ngOnDestroy() {
    // Limpiar suscripciones para evitar memory leaks
    this.destroy$.next();
    this.destroy$.complete();

    // Limpiar variables inválidas
    _.remove(this.variables, v2 => this.isInvalidValidName(v2.Name, v2.Id));
  }

  private setupSearch() {
    this.searchDebouncer.pipe(
      debounceTime(300),
      takeUntil(this.destroy$)
    ).subscribe(searchTerm => {
      this.filterVariables(searchTerm);
    });
  }

  private filterVariables(searchTerm: string) {
    if (!searchTerm || searchTerm.trim() === '') {
      this.variables = this.allVariables.slice();
    } else {
      const term = searchTerm.toLowerCase();
      this.variables = this.allVariables.filter(v => {
        const nameMatch = v.Name.toLowerCase().includes(term);
        const descriptionMatch = v.Description ? v.Description.toLowerCase().includes(term) : false;
        const defaultValueMatch = v.DefaultValue ? v.DefaultValue.toLowerCase().includes(term) : false;
        return nameMatch || descriptionMatch || defaultValueMatch;
      });
    }
    this.resetList();
  }

  private loadMoreVariables() {
    if (this.isLoadingMore) return;

    this.isLoadingMore = true;

    if (!this.destroy$.closed) {
      this.cdr.detectChanges();
    }

    const start = this.currentPage * this.pageSize;
    const end = start + this.pageSize;
    const newVariables = this.variables.slice(start, end);

    if (newVariables.length > 0) {
      this.displayedVariables.push(...newVariables);
      this.currentPage++;

      // Verificar si el componente no está destruido antes de detectChanges
      if (!this.destroy$.closed) {
        this.cdr.detectChanges();
      }
    }

    this.isLoadingMore = false;

    // Verificar si el componente no está destruido antes de detectChanges
    if (!this.destroy$.closed) {
      this.cdr.detectChanges();
    }
  }

  onSearch(text: string) {
    this.currentSearch = text;
    this.searchDebouncer.next(text || '');
  }

  onCloseSearch() {
    this.isSearchActive = false;
    this.resetList();
  }

  onCleanSearch() {
    this.currentSearch = null;
    this.searchDebouncer.next('');
  }

  private resetList() {
    this.displayedVariables = [];
    this.currentPage = 0;
    this.loadMoreVariables();
  }

  addVar() {
    const newVariable = this.editorService.createNewVariableWithName(null);
    this.variables = [newVariable, ...this.variables];
    this.displayedVariables = [];
    this.currentPage = 0;
    this.loadMoreVariables();

    // Verificar si el componente no está destruido antes de detectChanges
    if (!this.destroy$.closed) {
      this.cdr.detectChanges();
    }

    setTimeout(() => {
        const container = this.variableContainer.nativeElement;
        container.scrollTop = 0;

        const firstVariableElement = container.querySelector('.variable-row');
        if (firstVariableElement) {
            firstVariableElement.focus();
        }
    }, 100);
  }

  empty(str) {
    return str == null || str.length == 0;
  }

  isInvalidValidName(str, id) {

    const cacheKey = `${str}-${id}`;
    if (this.validationCache.has(cacheKey)) {
        //console.timeEnd('ValidateName');
        return this.validationCache.get(cacheKey);
    }

    if (this.empty(str)) {
        //console.timeEnd('ValidateName');
        this.validationCache.set(cacheKey, true);
        return true;
    }

    if (str === 'index' || str === 'item') {
        //console.timeEnd('ValidateName');
        this.validationCache.set(cacheKey, true);
        return true;
    }

    var found: VariableDefinition;
    if ((found = this.editorService.findVariableWithName(str, true)) != null &&
        found.Id != id) {
        //console.timeEnd('ValidateName');
        this.validationCache.set(cacheKey, true);
        return true;
    }

    let pattern = /^[a-zA-Z][a-zA-Z0-9_]{2,}$/;
    const result = !pattern.test(str);
    //console.timeEnd('ValidateName');
    this.validationCache.set(cacheKey, result);
    return result;
  }

  deleteVar(varDef: VariableDefinition) {
    const emmitAction = new EventEmitter();
    emmitAction.subscribe(() => {
      this.editorService.deleteVariable(varDef.Id);
      this.allVariables = this.allVariables.filter(v => v.Id !== varDef.Id);
      this.variables = this.allVariables.slice();
      this.resetList();

      if (!this.destroy$.closed) {
        this.cdr.detectChanges();
      }
    });

    var deleteInfo: DeleteInput = new DeleteInput();
    deleteInfo.ElementName = varDef.Name;

    ////console.time('FindReferencingBlocks');
    this.editorService.findBlocksReferencingVariable(varDef).forEach(blockId => {
      let block = this.editorService.findBlockWithId(blockId);
      if (block != null) {
        deleteInfo.AffectedElements.push(block.Name);
      }
    });
    //console.timeEnd('FindReferencingBlocks');

    this.modalService.init(
      DeleteGroupQuestionComponent,
      {
        DeleteDetail: deleteInfo,
        Title: 'ARE_YOU_SURE_DELETE_VARIABLE_QUESTION',
        HideAffected: deleteInfo.AffectedElements.length === 0,
        HideConfirmation: deleteInfo.AffectedElements.length === 0,
        deleteText: 'ACCEPT',
        DeleteTextExplanation: 'DELETE_VARIABLE_EXPLANATION',
      }, { DeleteAction: emmitAction }
    );
    //console.timeEnd('DeleteVariable');
  }

  canHaveDefaultValue(varDef: VariableDefinition): boolean {
    return !(varDef.Type === TypeDefinition.ByteArray ||
      varDef.Type === TypeDefinition.Any);
  }

  defaultValueChanged(varDef: VariableDefinition) {
    if (!this.hasDefaultValue(varDef)) {
      varDef.Constant = false;
    }
  }

  hasDefaultValue(varDef: VariableDefinition): boolean {
    if (!this.canHaveDefaultValue(varDef)) {
      return false;
    }

    if (!this.isDefaultValueValid(varDef)) {
      return false;
    }

    if (typeof (varDef.DefaultValue) === 'undefined' ||
      varDef.DefaultValue === null ||
      varDef.DefaultValue.length === 0) {
      return false;
    }

    return true;
  }

  isDefaultValueValid(varDef: VariableDefinition) {
    //console.time('ValidateDefaultValue');
    try {
      let type: TypeDefinition = varDef.Type;
      let value: string = varDef.DefaultValue;

      if (type === TypeDefinition.Text) {
        //console.timeEnd('ValidateDefaultValue');
        return true;
      }

      if (this.empty(value)) {
        //console.timeEnd('ValidateDefaultValue');
        return true;
      }

      let result = true;
      switch (type) {
        case TypeDefinition.Number:
        case TypeDefinition.Decimal: {
          let numberValue: number = Number(value);
          if (isNaN(numberValue)) {
            return false;
          }
          return numberValue.toString() === value;
        }

        case TypeDefinition.Bool: {
          let boolValue: boolean = JSON.parse(value);
          return boolValue != null;
        }

        case TypeDefinition.Array: {
          try {
            return Array.isArray(JSON.parse(value));
          }
          catch (e) {
            return false;
          }
        }

        case TypeDefinition.Object: {
          try {
            JSON.parse(value);
            return true;
          }
          catch (e) {
            return false;
          }
        }

        case TypeDefinition.StringDate: {
          let formats = [
            moment.ISO_8601,
            "D/M/YYYY",
            "D/M/YY",
            "D-M-YYYY",
            "D-M-YY",
          ];
          return moment(value, formats, true).isValid();
        }

        case TypeDefinition.Timestamp: {
          let numberValue: number = Number.parseInt(value);
          if (isNaN(numberValue)) {
            return false;
          }
          return numberValue > 0 && numberValue.toString() === value;
        }

        case TypeDefinition.Base64: {
          let base64regex = /^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/;

          return base64regex.test(value);
        }

      }
      //console.timeEnd('ValidateDefaultValue');
      return true;
    }
    catch (exp) {
      //console.timeEnd('ValidateDefaultValue');
      return false;
    }
  }

  importVariables() {
    let emitAction = new EventEmitter<VariableDefinition[]>();
    emitAction.subscribe((variables: VariableDefinition[]) => {
      if (variables !== null) {
        variables.forEach(v => this.editorService.cloneVariable(v));
        this.allVariables = this.editorService.getVariableList();
        this.variables = this.allVariables.slice();
        this.resetList();

        if (!this.destroy$.closed) {
          this.cdr.detectChanges();
        }
      }
    });

    let flow = this.editorService.getCurrentFlow();
    this.modalService.init(
      FlowVariablesSelectorDialogComponent,
      {
        FlowToIgnore: flow.id,
      },
      { AcceptAction: emitAction });
  }
}
