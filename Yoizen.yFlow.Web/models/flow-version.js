var Sequelize = require('sequelize');
var User = require('./user');
var UserAction = require('./user-action');
const sequelize = require('../helpers/sequelize');
const { addNewVersion, getFlowVersion } = require('../helpers/flowVersionManager');
const CryptoJS = require('crypto-js');
const zlib = require('zlib');
const Salt = CryptoJS.enc.Utf8.parse("Y01z3n2020*");

if (typeof process.env.iv !== "string")
    process.env.iv = "YJY2yfEyqHiGeS7dRnDSKw==";
if (typeof process.env.key !== "string")
    process.env.key = "6+0WAvwNXuEtpst2l6ruHHBdHLUVfTVFJQhsECxNUPI=";

const iv = CryptoJS.PBKDF2(process.env.iv.toString(CryptoJS.enc.Utf8), Salt, { keySize: 128 / 32, iterations: 100 });
//Creating the key in PBKDF2 format to be used during the decryption
const key = CryptoJS.PBKDF2(process.env.key.toString(CryptoJS.enc.Utf8), Salt, { keySize: 256 / 32, iterations: 100 });
const regex = /^\*{1,10}$/;
var FlowVersion = sequelize.define('flow_version', {
    number: {
        type: Sequelize.INTEGER
    },
    blob: {
        type: Sequelize.STRING
    },
    stats: {
        type: Sequelize.STRING
    },
    published_at: {
        type: Sequelize.DATE,
    },
    published_by_user_id: {
        type: Sequelize.INTEGER
    },
    comments: {
        type: Sequelize.STRING
    },
    flow_id: {
        type: Sequelize.INTEGER
    }
}, {
    updatedAt: false
});

FlowVersion.findById = async function (id, decryptPrivateVariables = false) {
    let version = await FlowVersion.findOne({ where: { id: id } });
    if (!version.blob) {
        version.blob = await getFlowVersion(version.flow_id, version.number);
    }
    // Descompresión condicional: solo si los datos están comprimidos (no comienzan con '{')
    if (version.blob && version.blob && !version.blob.startsWith('{')) {
        // Intentar descomprimir solo si parece estar comprimido
        try {
            const compressedData = Buffer.from(version.blob, 'base64');
            version.blob = zlib.unzipSync(compressedData).toString();
        } catch (error) {
            // Si falla la descompresión, asumir que no está comprimido
            console.warn(`Flow version ${id} no requiere descompresión o formato no válido`);
        }
    }

    if (decryptPrivateVariables) {
        version.blob = FlowVersion.showPrivateVariables(version.blob);
    }
    return version
}

FlowVersion.findByIdAndVersion = async function (blob, id, version, decryptPrivateVariables = false) {
    if (typeof (blob) === 'string' && blob.length > 0) {
        // Descompresión condicional: solo si los datos están comprimidos
        if (!blob.startsWith('{')) {
            try {
                const compressedData = Buffer.from(blob, 'base64');
                blob = zlib.unzipSync(compressedData).toString();
            } catch (error) {
                // Si falla la descompresión, asumir que no está comprimido
                console.warn(`Flow version ${id}:${version} no requiere descompresión o formato no válido`);
            }
        }
    } else {
        blob = await getFlowVersion(id, version);
    }
    if (decryptPrivateVariables) {
        blob = FlowVersion.showPrivateVariables(blob);
    }
    return blob
}

/**
 * Método Deprecado!!!
 * @param {*} flowId 
 * @param {*} decryptPrivateVariables 
 * @returns 
 */
FlowVersion.findLastVersion = async function (flowId, decryptPrivateVariables = false) {
    throw new Error('Deprecated');
    let version = await FlowVersion.findOne({
        limit: 1,
        where: {
            flow_id: flowId,
        },
        order: [['created_at', 'DESC']]
    });

    if (!version.blob) {
        version.blob = getFlowVersion(version.flow_id, version.number);
    }
    if (decryptPrivateVariables) {
        version.blob = FlowVersion.showPrivateVariables(version.blob);
    }
    return version;
}

FlowVersion.createNewVersion = async function (blob, flowId, comments, userId, companyId) {
    var prevVersion = await FlowVersion.findOne({
        attributes: ['number'],
        where: {
            flow_id: flowId
        },
        order: [
            ['number', 'DESC']
        ]
    });

    var lastNumber = 0;
    if (prevVersion != null) {
        lastNumber = prevVersion.number;
    }
    const t = await sequelize.transaction();

    try {

        let blobJson = JSON.parse(blob);
        let arePrivateVariables = false;

        blobJson.VariableList.forEach(variable => {
            if (variable.Private) {
                var decrypted = CryptoJS.AES.decrypt(variable.DefaultValue, key, {
                    mode: CryptoJS.mode.CBC,
                    padding: CryptoJS.pad.AnsiX923,
                    iv: iv
                });
                //si es vacio, significa que me cambio los valores, así que es una dato nuevo
                if (decrypted.toString(CryptoJS.enc.Utf8) === '') {
                    var encrypted = CryptoJS.AES.encrypt(variable.DefaultValue, key, {
                        mode: CryptoJS.mode.CBC,
                        padding: CryptoJS.pad.AnsiX923,
                        iv: iv
                    });
                    variable.DefaultValue = encrypted.toString();
                    arePrivateVariables = true;
                }
            }
        });        
        if (arePrivateVariables) {
            blob = JSON.stringify(blobJson);
        }

        const newFlowVersion = await FlowVersion.create({
            flow_id: flowId,
            blob: blob,
            number: lastNumber + 1,
            stats: null,
            comments: comments,
            user_id: userId,
            company_id: companyId
        });

        /*const isCreated = await addNewVersion(blob, flowId, newFlowVersion.number);
        if(!isCreated) {
            return null;
        }]*/

        t.commit();
        return newFlowVersion;
    } catch (err) {
        await t.rollback();
        console.error(`error en flowVersion.createNewVersion, ${err}`);
        return null;
    }

};

FlowVersion.showPrivateVariables = function (blob) {
    let blobJson = JSON.parse(blob);
    let arePrivateVariables = false;

    blobJson.VariableList.forEach(variable => {
        if (variable.Private) {
            var encrypted = CryptoJS.AES.decrypt(variable.DefaultValue, key, {
                mode: CryptoJS.mode.CBC,
                padding: CryptoJS.pad.AnsiX923,
                iv: iv
            });
            variable.DefaultValue = encrypted.toString(CryptoJS.enc.Utf8);
            arePrivateVariables = true;
        }
    });

    if (arePrivateVariables) {
        blob = JSON.stringify(blobJson);
    }
    return blob;
};

FlowVersion.hasMany(UserAction);
FlowVersion.belongsTo(User, {
    foreignKey: 'user_id'
});
module.exports = FlowVersion;