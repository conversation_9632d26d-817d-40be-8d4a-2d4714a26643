// noinspection RegExpRedundantEscape
var express = require('express');
var router = express.Router();
var Flow = require('../models/flow.js');
var FlowVersion = require('../models/flow-version');
var User = require('../models/user');
var UserAction = require('../models/user-action');
var UserPermissionFlow = require('../models/user-permission-flow');
var ErrorHandler = require('../helpers/error-handler');
//var FlowExecutor = require('../helpers/flow-executor');
var DiffHelper = require('../helpers/flow-diff-helper');
var axios = require('axios');
var authorization = require('../helpers/authorization').addAuthorizationHeaders;
var moment = require('moment');
var app = require('../app.js');
const { Op } = require("sequelize");
var crypto = require('crypto');
var md5 = require('md5');
const DetailedDefaultAnswer = require('../models/detailed/detailedDefaultAnswer');
const Daily = require('../models/historical/daily');
const DailyByBlocksSequence = require('../models/historical/dailyByBlocksSequence');
const DailyByGroupsSequence = require('../models/historical/dailyByGroupsSequence');
const DailyByFlow = require('../models/historical/dailyByFlow');
const DailyByCommands = require('../models/historical/dailyByCommands');
const DailyByDerivationKey = require('../models/historical/dailyByDerivationKey');
const SocialServiceType = require('../helpers/social-service-types');
const { auditService } = require('../services/audit.service');
const { localeServices } = require('../i18n.config.js');
const auditTranslations = require('../translations/audit.translations');
const EntityTypes = require('../helpers/entity-types');

const contextFolder = require('../helpers/folders.js').contextFolder;
const fs = require('fs');
const { logger } = require('../helpers/pino-logger');

// Crear directorio temporal para chunks si no existe
const tempChunksDir = `${contextFolder}/temp`;
if (!fs.existsSync(tempChunksDir)) {
    fs.mkdirSync(tempChunksDir, { recursive: true });
    logger.debug(`[INIT] Directorio temporal para chunks creado: ${tempChunksDir}`);
}

// Limpiar directorios temporales antiguos al iniciar
try {
    if (fs.existsSync(tempChunksDir)) {
        const subdirs = fs.readdirSync(tempChunksDir);
        const now = new Date().getTime();
        const ONE_DAY = 24 * 60 * 60 * 1000; // 24 horas en milisegundos

        for (const subdir of subdirs) {
            try {
                const subdirPath = `${tempChunksDir}/${subdir}`;
                const stats = fs.statSync(subdirPath);

                // Si el directorio tiene más de 1 día, eliminarlo
                if (now - stats.mtimeMs > ONE_DAY) {
                    logger.debug(`[CLEANUP] Eliminando directorio temporal antiguo: ${subdirPath}`);

                    // Eliminar todos los archivos dentro del directorio
                    const files = fs.readdirSync(subdirPath);
                    for (const file of files) {
                        fs.unlinkSync(`${subdirPath}/${file}`);
                    }

                    // Eliminar el directorio
                    fs.rmdirSync(subdirPath);
                }
            } catch (error) {
                logger.error(`[ERROR] Error al limpiar directorio temporal antiguo: ${error}`);
            }
        }
    }
} catch (error) {
    logger.error(`[ERROR] Error al limpiar directorios temporales antiguos: ${error}`);
}

let cognitivityApiVersion = "1";
if (typeof (process.env.cognitivityApiVersion) !== 'undefined') {
    cognitivityApiVersion = process.env.cognitivityApiVersion;
}

if (typeof (process.env.hostInsideIIS) === 'string') {
    process.env.hostInsideIIS = process.env.hostInsideIIS === 'true';
}

let executorUrl = process.env.executorUrl;
if (!executorUrl.endsWith('/')) {
    executorUrl += '/';
}

const client = process.env.client;

let myCache = require('../helpers/mycache');

let redis = null;
redis = require('../helpers/redis');

redis.flowsClient.on("subscribe", function (channel) {
    logger.info(`Se suscribió al canal ${channel}`);
});

redis.flowsClient.on('message', function (channel, message) {
    logger.info(`Se recibió mensaje por el canal ${channel} con mensaje ${message}`);

    let info = JSON.parse(message);
    if (typeof (info.action) === 'string') {
        switch (info.action) {
            case 'publish':
                if (myCache.has(info.cacheKey)) {
                    myCache.del(info.cacheKey);
                }
                break;
            case 'saving':
                if (myCache.has(info.cacheKey)) {
                    myCache.del(info.cacheKey);
                }
                break;
            case 'smtpconfig':
                let smtpCacheKey = `${client}-smtp`;
                if (myCache.has(smtpCacheKey)) {
                    myCache.del(smtpCacheKey);
                }
                break;
            default:
                logger.error(`No hay programado para multicores procesar el evento ${info.action}`);
                break;
        }
    }
});

redis.flowsClient.subscribe(client);

const Configurations = require("../models/configurations.js");
const https = require("https");
const e = require('express');
const fsPromise = require("fs").promises;
const zlib = require('zlib');

let standAlone = true;
if (process.env.standAlone === 'false') {
    standAlone = false;
}

let generateFilesForDefaultAnswerBlock = false;
if (typeof (process.env.generateFilesForDefaultAnswerBlock) === 'string' &&
    process.env.generateFilesForDefaultAnswerBlock.toLowerCase() === 'true') {
    generateFilesForDefaultAnswerBlock = true;
}

// noinspection JSUnresolvedFunction
class FlowsController {
    constructor() {
    }

    /**
     * Elimina archivos temporales después de procesar una carga
     * @param {string} tempDir Directorio temporal
     */
    cleanupTempFiles(tempDir) {
        try {
            if (fs.existsSync(tempDir)) {
                // Eliminar todos los archivos en el directorio
                const files = fs.readdirSync(tempDir);
                for (const file of files) {
                    fs.unlinkSync(`${tempDir}/${file}`);
                }

                // Eliminar el directorio
                fs.rmdirSync(tempDir);
                logger.debug(`[CLEANUP] Archivos temporales eliminados: ${tempDir}`);
            }
        } catch (error) {
            logger.error(`error: ${error}`);
        }
    }

    getUserLocale(req) {
        return req.headers['accept-language'] || localeServices.getCurrentLocale();
    }

    async getFlows(req, res) {
        let includeModules = req.query.includeModules !== undefined ? req.query.includeModules.toLowerCase() === 'true' : false;
        Flow.findAllByCompany(req.user.cid, req.user.uid, includeModules).then((flows) => {
            return res.send(flows);
        }).catch((err) => {
            logger.error(`error en flows.getFlows, ${err}`);
            return res.status(500).send({ success: false, message: 'fatal error' });
        });
    }

    /**
     * Obtiene información básica de un flujo sin incluir el blob completo (método interno)
     * @param {string} id ID del flujo
     * @param {string} cid ID de la compañía
     * @param {string} uid ID del usuario
     * @returns {Object} Información básica del flujo
     */
    async getFlowInfoInternal(id, cid, uid) {
        try {
            let flowId = id;
            let flowWithoutVersion = await Flow.findOneByIdWithoutVersion(id, cid);

            if (!flowWithoutVersion) {
                return null;
            }

            if (flowWithoutVersion.master_flow_id !== null) {
                flowId = flowWithoutVersion.master_flow_id;
            }

            let cacheKey = `${client}-${flowId}-staging`;
            let flow;

            if (myCache.has(cacheKey)) {
                flow = myCache.get(cacheKey);
            } else {
                flow = await Flow.findOneById(flowId, cid, uid);

                if (!flow) {
                    return null;
                }
            }

            // Obtener el tamaño del blob
            let blobSize = 0;
            let compressedBlobSize = 0;
            let totalChunks = 0;
            const CHUNK_SIZE = 900 * 1024; // 900KB

            if (flow.ActiveStagingVersion && flow.ActiveStagingVersion.blob) {
                const blob = typeof flow.ActiveStagingVersion.blob === 'string'
                    ? flow.ActiveStagingVersion.blob
                    : JSON.stringify(flow.ActiveStagingVersion.blob);

                blobSize = Buffer.byteLength(blob, 'utf8');

                // Comprimir el blob para calcular el tamaño comprimido
                try {
                    const JSZip = require('jszip');
                    const zip = new JSZip();
                    zip.file('flow.json', blob);

                    const compressedContent = await zip.generateAsync({
                        type: 'nodebuffer',
                        compression: 'DEFLATE',
                        compressionOptions: {
                            level: 7
                        }
                    });

                    compressedBlobSize = compressedContent.length;

                    // Calcular el número de fragmentos basado en el tamaño comprimido
                    // Esto asegura que el número de fragmentos sea consistente con la importación
                    totalChunks = Math.ceil(compressedBlobSize / CHUNK_SIZE);

                    logger.debug(`[INFO] Flujo ${flow.name} (ID: ${flow.id}): Tamaño original: ${Math.round(blobSize/1024)} KB, Tamaño comprimido: ${Math.round(compressedBlobSize/1024)} KB, Fragmentos: ${totalChunks}`);
                } catch (error) {
                    logger.error(`Error al comprimir blob para calcular tamaño: ${error}`);
                    // Si falla la compresión, usar el tamaño original
                    compressedBlobSize = blobSize;
                    totalChunks = Math.ceil(blobSize / CHUNK_SIZE);
                }
            }

            // Crear una copia del flujo sin el blob para reducir el tamaño de la respuesta
            const flowInfo = {
                id: flow.id,
                name: flow.name,
                channel: flow.channel,
                type: flow.type,
                isMasterModule: flowWithoutVersion.isMasterModule,
                isMasterBot: flow.isMasterBot,
                blobSize: blobSize,
                compressedBlobSize: compressedBlobSize,
                totalChunks: totalChunks,
                chunkSize: CHUNK_SIZE,
                ActiveStagingVersionId: flow.ActiveStagingVersionId,
                ActiveProductionVersionId: flow.ActiveProductionVersionId,
                ActiveProductionVersionMasterId: flow.ActiveProductionVersionMasterId
            };

            // Incluir información adicional sobre las versiones activas
            if (flow.ActiveStagingVersion) {
                flowInfo.ActiveStagingVersion = {
                    id: flow.ActiveStagingVersionId,
                    number: flow.ActiveStagingVersion.number
                };
            }

            if (flow.ActiveProductionVersion) {
                flowInfo.ActiveProductionVersion = {
                    id: flow.ActiveProductionVersionId,
                    number: flow.ActiveProductionVersion.number
                };
            }

            return flowInfo;
        } catch (error) {
            logger.error(`Error al obtener información del flujo: ${error}`);
            return null;
        }
    }

    /**
     * Obtiene información básica de un flujo sin incluir el blob completo
     * @param {Object} req Solicitud HTTP
     * @param {Object} res Respuesta HTTP
     * @returns {Object} Información básica del flujo
     */
    async getFlowInfo(req, res) {
        try {
            const flowInfo = await this.getFlowInfoInternal(req.params.id, req.user.cid, req.user.uid);

            if (!flowInfo) {
                return res.status(404).send({ success: false, message: 'Flow NOT FOUND' });
            }

            return res.status(200).send(flowInfo);
        } catch (error) {
            logger.error(`Error al obtener información del flujo: ${error}`);
            return res.status(500).send({ success: false, message: 'Error al obtener información del flujo' });
        }
    }

    /**
     * Obtiene un fragmento específico de un flujo
     * @param {Object} req Solicitud HTTP
     * @param {Object} res Respuesta HTTP
     * @returns {Object} Fragmento del flujo
     */
    async getFlowChunk(req, res) {
        try {
            const { id, chunkIndex } = req.params;
            const chunkIdx = parseInt(chunkIndex, 10);

            if (isNaN(chunkIdx) || chunkIdx < 0) {
                return res.status(400).send({ success: false, message: 'Índice de fragmento inválido' });
            }

            // Obtener información del flujo usando el método interno
            const flowInfo = await this.getFlowInfoInternal(id, req.user.cid, req.user.uid);

            if (!flowInfo) {
                return res.status(404).send({ success: false, message: 'Flow NOT FOUND' });
            }

            // Verificar que el índice del fragmento sea válido
            if (chunkIdx >= flowInfo.totalChunks) {
                return res.status(400).send({
                    success: false,
                    message: `Índice de fragmento fuera de rango. Total fragmentos: ${flowInfo.totalChunks}`
                });
            }

            // Obtener el flujo completo
            let flowId = id;
            let flowWithoutVersion = await Flow.findOneByIdWithoutVersion(id, req.user.cid);

            if (flowWithoutVersion.master_flow_id !== null) {
                flowId = flowWithoutVersion.master_flow_id;
            }

            let cacheKey = `${client}-${flowId}-staging`;
            let flow;

            if (myCache.has(cacheKey)) {
                flow = myCache.get(cacheKey);
            } else {
                flow = await Flow.findOneById(flowId, req.user.cid, req.user.uid);
            }

            // Obtener el blob
            if (!flow.ActiveStagingVersion || !flow.ActiveStagingVersion.blob) {
                return res.status(404).send({ success: false, message: 'Flow blob NOT FOUND' });
            }

            // Incluir información sobre la versión activa en los encabezados
            res.setHeader('X-Active-Staging-Version-Id', flow.ActiveStagingVersionId || '');
            res.setHeader('X-Active-Staging-Version-Number', flow.ActiveStagingVersion.number || '');
            res.setHeader('X-Active-Production-Version-Id', flow.ActiveProductionVersionId || '');
            if (flow.ActiveProductionVersion) {
                res.setHeader('X-Active-Production-Version-Number', flow.ActiveProductionVersion.number || '');
            }

            const blob = typeof flow.ActiveStagingVersion.blob === 'string'
                ? flow.ActiveStagingVersion.blob
                : JSON.stringify(flow.ActiveStagingVersion.blob);

            // Comprimir el blob completo
            const JSZip = require('jszip');
            const zip = new JSZip();
            zip.file('flow.json', blob);

            const compressedContent = await zip.generateAsync({
                type: 'nodebuffer',
                compression: 'DEFLATE',
                compressionOptions: {
                    level: 7
                }
            });

            // Calcular el tamaño del blob comprimido
            const compressedSize = compressedContent.length;
            const CHUNK_SIZE = 900 * 1024; // 900KB
            const totalChunks = Math.ceil(compressedSize / CHUNK_SIZE);

            // Verificar que el número de fragmentos coincida con el calculado previamente
            if (totalChunks !== flowInfo.totalChunks) {
                logger.warn(`[ADVERTENCIA] El número de fragmentos calculado (${totalChunks}) no coincide con el esperado (${flowInfo.totalChunks})`);
            }

            // Dividir el blob comprimido en fragmentos
            const start = chunkIdx * CHUNK_SIZE;
            const end = Math.min(start + CHUNK_SIZE, compressedSize);

            // Extraer el fragmento del blob comprimido
            // Crear un nuevo buffer con el fragmento
            const chunkBuffer = Buffer.alloc(end - start);
            compressedContent.copy(chunkBuffer, 0, start, end);

            // Verificar si el cliente acepta respuestas comprimidas
            const acceptZip = req.headers['x-accept-zip'] === 'true';

            if (acceptZip) {
                // El fragmento ya está comprimido, enviarlo directamente
                res.setHeader('Content-Type', 'application/octet-stream');
                res.setHeader('X-Content-Encoding', 'zip');
                res.setHeader('X-Chunk-Index', chunkIdx.toString());
                res.setHeader('X-Total-Chunks', totalChunks.toString());
                res.setHeader('X-Compressed-Fragment', 'true');
                return res.send(chunkBuffer);
            } else {
                // Descomprimir el fragmento para clientes que no aceptan compresión
                // Esto es poco común, pero lo incluimos por compatibilidad
                try {
                    // Crear un nuevo ZIP con solo este fragmento
                    const fragmentZip = new JSZip();
                    fragmentZip.file('chunk.bin', chunkBuffer);

                    // Convertir a base64 para enviar como texto
                    const base64Data = chunkBuffer.toString('base64');

                    // Enviar respuesta sin comprimir
                    res.setHeader('Content-Type', 'text/plain');
                    res.setHeader('X-Chunk-Index', chunkIdx.toString());
                    res.setHeader('X-Total-Chunks', totalChunks.toString());
                    res.setHeader('X-Compressed-Fragment', 'true');
                    return res.send(base64Data);
                } catch (error) {
                    logger.error(`Error al procesar fragmento para cliente sin soporte de compresión: ${error}`);
                    return res.status(500).send({
                        success: false,
                        message: 'Error al procesar fragmento. El cliente debe aceptar compresión.'
                    });
                }
            }
        } catch (error) {
            logger.error(`Error al obtener fragmento del flujo: ${error}`);
            return res.status(500).send({ success: false, message: 'Error al obtener fragmento del flujo' });
        }
    }

    async getFlow(req, res, sendRes = true) {
        // Verificar si se solicita la carga en fragmentos
        const useChunks = req.query.chunked === 'true';

        // Siempre usar fragmentos para flujos grandes (>800KB)
        // Primero obtener información básica del flujo
        const flowInfo = await this.getFlowInfoInternal(req.params.id, req.user.cid, req.user.uid);

        // Si el flujo es grande (>800KB), usar carga en fragmentos
        if (flowInfo && flowInfo.blobSize > 800 * 1024) {
            logger.debug(`[CHUNKING] El flujo es grande (${Math.round(flowInfo.blobSize / 1024)} KB), usando carga en fragmentos`);
            return this.getFlowInfo(req, res);
        }

        if (useChunks) {
            // Si se solicita en fragmentos, redirigir a getFlowInfo
            return this.getFlowInfo(req, res);
        }

        let flowId = req.params.id;
        let flowWithoutVersion = await Flow.findOneByIdWithoutVersion(req.params.id, req.user.cid);
        let flow;
        if (!flowWithoutVersion) {
            logger.error(`error en flows.getFlow, ${err}`);
            return res.status(500).send({ success: false, message: 'fatal error' });
        }

        if (flowWithoutVersion.master_flow_id !== null) {
            flowId = flowWithoutVersion.master_flow_id;
        }

        let cacheKey = `${client}-${flowId}-staging`;

        //TODO: TERMINAR LO DE CACHE
        if (myCache.has(cacheKey)) {
            flow = myCache.get(cacheKey);
            flowWithoutVersion = JSON.parse(JSON.stringify(flowWithoutVersion));
            flowWithoutVersion.isMasterModule = flowId === req.params.id;
            flowWithoutVersion.isMasterBot = flow.isMasterBot;
            flowWithoutVersion.ActiveProductionVersion = flow.ActiveProductionVersion;
            flowWithoutVersion.ActiveStagingVersion = {};
            flowWithoutVersion.ActiveStagingVersion.id = flow.ActiveStagingVersion.id;
            flowWithoutVersion.ActiveStagingVersion.blob = flow.ActiveStagingVersion.blob;
            flowWithoutVersion.ActiveStagingVersion.flowId = flow.ActiveStagingVersion.flowId;
            flowWithoutVersion.ActiveStagingVersion.flow_id = flow.ActiveStagingVersion.flow_id;
            flowWithoutVersion.ActiveStagingVersion.number = flow.ActiveStagingVersion.number;
            flowWithoutVersion.ActiveProductionVersionMasterId = flow.ActiveProductionVersionMasterId;
            if (flowId === req.params.id) {
                if (sendRes) {
                    return res.send(flowWithoutVersion);
                }
                else {
                    return flowWithoutVersion
                }
            } else {
                logger.debug(`[GETFLOW] Obteniendo módulo child ${req.params.id}`);
                let flowModule = await Flow.findOneById(req.params.id, req.user.cid, req.user.uid);

                logger.debug(`[GETFLOW] FlowModule obtenido: ${flowModule ? 'existe' : 'null'}`);
                if (flowModule) {
                    logger.debug(`[GETFLOW] FlowModule.ActiveStagingVersion: ${flowModule.ActiveStagingVersion ? 'existe' : 'null'}`);
                    if (flowModule.ActiveStagingVersion) {
                        logger.debug(`[GETFLOW] FlowModule.ActiveStagingVersion.blob tipo: ${typeof flowModule.ActiveStagingVersion.blob}`);
                        logger.debug(`[GETFLOW] FlowModule.ActiveStagingVersion.blob valor: ${flowModule.ActiveStagingVersion.blob}`);
                    }
                }

                if (!flowModule || !flowModule.ActiveStagingVersion || !flowModule.ActiveStagingVersion.blob) {
                    logger.error(`error en flows.getFlow: flowModule or blob is null/undefined for module ${req.params.id}`);
                    return res.status(500).send({ success: false, message: 'fatal error: missing module blob data' });
                }
                
                if (!flowWithoutVersion.ActiveStagingVersion || !flowWithoutVersion.ActiveStagingVersion.blob) {
                    logger.error(`error en flows.getFlow: ActiveStagingVersion or blob is null/undefined for flow ${req.params.id}`);
                    return res.status(500).send({ success: false, message: 'fatal error: missing blob data' });
                }
                
                if (typeof (flowWithoutVersion.ActiveStagingVersion.blob) === 'string') {
                    try {
                        flowWithoutVersion.ActiveStagingVersion.blob = JSON.parse(flowWithoutVersion.ActiveStagingVersion.blob);
                    } catch (parseError) {
                        logger.error(`error parsing blob JSON for flow ${req.params.id}: ${parseError}`);
                        return res.status(500).send({ success: false, message: 'fatal error: invalid blob JSON' });
                    }
                }

                if (!flowWithoutVersion.ActiveStagingVersion.blob || typeof flowWithoutVersion.ActiveStagingVersion.blob !== 'object') {
                    logger.error(`error en flows.getFlow: blob is not a valid object for flow ${req.params.id}`);
                    return res.status(500).send({ success: false, message: 'fatal error: invalid blob object' });
                }

                flowWithoutVersion.ActiveStagingVersion.blob.AllModules = await Flow.findAllTesterModulesById(flowId, false, false);

                if (flowWithoutVersion.ActiveStagingVersion.blob.BlockGroups && Array.isArray(flowWithoutVersion.ActiveStagingVersion.blob.BlockGroups)) {
                    flowWithoutVersion.ActiveStagingVersion.blob.BlockGroups = flowWithoutVersion.ActiveStagingVersion.blob.BlockGroups.filter(blockGroup => {
                        return blockGroup.ModuleId !== flowWithoutVersion.id
                    });
                } else {
                    flowWithoutVersion.ActiveStagingVersion.blob.BlockGroups = [];
                }

                if (flowWithoutVersion.ActiveStagingVersion.blob.BlockList && Array.isArray(flowWithoutVersion.ActiveStagingVersion.blob.BlockList)) {
                    flowWithoutVersion.ActiveStagingVersion.blob.BlockList = flowWithoutVersion.ActiveStagingVersion.blob.BlockList.filter(block => {
                        return block.ModuleId !== flowWithoutVersion.id
                    });
                } else {
                    flowWithoutVersion.ActiveStagingVersion.blob.BlockList = [];
                }

                flowModule = JSON.parse(JSON.stringify(flowModule));
                let masterBlob = flowModule.ActiveStagingVersion.blob;
                if (typeof (masterBlob) === 'string') {
                    try {
                        masterBlob = JSON.parse(masterBlob);
                    } catch (parseError) {
                        logger.error(`error parsing masterBlob JSON for module ${req.params.id}: ${parseError}`);
                        return res.status(500).send({ success: false, message: 'fatal error: invalid masterBlob JSON' });
                    }
                }

                // Ensure masterBlob is a valid object
                if (!masterBlob || typeof masterBlob !== 'object') {
                    logger.error(`error en flows.getFlow: masterBlob is not a valid object for module ${req.params.id}`);
                    return res.status(500).send({ success: false, message: 'fatal error: invalid masterBlob object' });
                }

                // Validate and push BlockGroups if they exist in masterBlob
                if (masterBlob.BlockGroups && Array.isArray(masterBlob.BlockGroups)) {
                    flowWithoutVersion.ActiveStagingVersion.blob.BlockGroups.push(...masterBlob.BlockGroups);
                }

                // Validate and push BlockList if they exist in masterBlob
                if (masterBlob.BlockList && Array.isArray(masterBlob.BlockList)) {
                    flowWithoutVersion.ActiveStagingVersion.blob.BlockList.push(...masterBlob.BlockList);
                }

                flowWithoutVersion.ActiveStagingVersion.blob = JSON.stringify(flowWithoutVersion.ActiveStagingVersion.blob)
                flowWithoutVersion = JSON.parse(JSON.stringify(flowWithoutVersion))
                flowWithoutVersion.isMasterModule = flowId === req.params.id;
                flowWithoutVersion.ActiveStagingVersion.number = flowModule.ActiveStagingVersion.number;

                logger.debug(`[GETFLOW] Enviando respuesta para módulo child. Blob tipo: ${typeof flowWithoutVersion.ActiveStagingVersion.blob}`);
                logger.debug(`[GETFLOW] Blob primeros 200 chars: ${flowWithoutVersion.ActiveStagingVersion.blob ? flowWithoutVersion.ActiveStagingVersion.blob.substring(0, 200) : 'null'}`);

                if (sendRes) {
                    return res.send(flowWithoutVersion);
                } else {
                    return flowWithoutVersion;
                }
            }
        } else {
            flow = await Flow.findOneById(flowId, req.user.cid, req.user.uid);;

            if (!flow) {
                logger.error(`error en flows.getFlows, ${err}`);
                return res.status(500).send({ success: false, message: 'fatal error' });
            }
            flow = JSON.parse(JSON.stringify(flow));
            var modules = await Flow.findAllModulesById(flowId, req.user.cid, req.user.uid);
            flow.isMasterBot = modules.length > 0;
            logger.debug("isMasterBot: ", flow.isMasterBot)

            let masterBlob = JSON.parse(flow.ActiveStagingVersion.blob);
            logger.debug(`[MASTERBOT] Blob original parseado. BlockGroups: ${masterBlob.BlockGroups ? 'existe' : 'undefined'}, BlockList: ${masterBlob.BlockList ? 'existe' : 'undefined'}`);

            // Inicializar arrays si no existen para evitar errores de deserialización
            if (!masterBlob.BlockGroups) {
                logger.debug(`[MASTERBOT] Inicializando BlockGroups vacío`);
                masterBlob.BlockGroups = [];
            }
            if (!masterBlob.BlockList) {
                logger.debug(`[MASTERBOT] Inicializando BlockList vacío`);
                masterBlob.BlockList = [];
            }

            modules.forEach(module => {
                let moduleBlob
                moduleBlob = module.ActiveStagingVersion !== null ? JSON.parse(module.ActiveStagingVersion.blob) : undefined;
                logger.debug(`[MASTERBOT] Procesando módulo ${module.id}. ActiveStagingVersion: ${module.ActiveStagingVersion !== null ? 'existe' : 'null'}`);

                if (moduleBlob !== undefined && moduleBlob !== null) {
                    logger.debug(`[MASTERBOT] Módulo ${module.id} - BlockGroups: ${moduleBlob.BlockGroups ? `array con ${moduleBlob.BlockGroups.length} elementos` : 'undefined'}, BlockList: ${moduleBlob.BlockList ? `array con ${moduleBlob.BlockList.length} elementos` : 'undefined'}`);

                    // Verificar que los arrays del módulo existan antes de hacer push
                    if (moduleBlob.BlockGroups && Array.isArray(moduleBlob.BlockGroups)) {
                        logger.debug(`[MASTERBOT] Agregando ${moduleBlob.BlockGroups.length} BlockGroups del módulo ${module.id}`);
                        masterBlob.BlockGroups.push(...moduleBlob.BlockGroups);
                    }
                    if (moduleBlob.BlockList && Array.isArray(moduleBlob.BlockList)) {
                        logger.debug(`[MASTERBOT] Agregando ${moduleBlob.BlockList.length} BlockList del módulo ${module.id}`);
                        masterBlob.BlockList.push(...moduleBlob.BlockList);
                    }
                } else {
                    logger.debug(`[MASTERBOT] Módulo ${module.id} no tiene blob válido`);
                }
            });

            logger.debug(`[MASTERBOT] Blob final - BlockGroups: ${masterBlob.BlockGroups.length} elementos, BlockList: ${masterBlob.BlockList.length} elementos`);
            flow.ActiveStagingVersion.blob = JSON.stringify(masterBlob);

            if (flow.ActiveProductionVersion !== null) {
                masterBlob = JSON.parse(flow.ActiveProductionVersion.blob);

                // Inicializar arrays si no existen para evitar errores de deserialización
                if (!masterBlob.BlockGroups) {
                    masterBlob.BlockGroups = [];
                }
                if (!masterBlob.BlockList) {
                    masterBlob.BlockList = [];
                }

                modules.forEach(module => {
                    let moduleBlob
                    moduleBlob = module.ActiveProductionVersionMaster !== null ? JSON.parse(module.ActiveProductionVersionMaster.blob) : undefined;

                    if (moduleBlob !== undefined && moduleBlob !== null) {
                        // Verificar que los arrays del módulo existan antes de hacer push
                        if (moduleBlob.BlockGroups && Array.isArray(moduleBlob.BlockGroups)) {
                            masterBlob.BlockGroups.push(...moduleBlob.BlockGroups);
                        }
                        if (moduleBlob.BlockList && Array.isArray(moduleBlob.BlockList)) {
                            masterBlob.BlockList.push(...moduleBlob.BlockList);
                        }
                    }
                });

                flow.ActiveProductionVersion.blob = JSON.stringify(masterBlob);
            }

            flowWithoutVersion = JSON.parse(JSON.stringify(flowWithoutVersion))
            flowWithoutVersion.isMasterModule = flowId === req.params.id;
            flowWithoutVersion.isMasterBot = flow.isMasterBot;
            flowWithoutVersion.ActiveProductionVersion = flow.ActiveProductionVersion;
            flowWithoutVersion.ActiveStagingVersion = flow.ActiveStagingVersion;
            flowWithoutVersion.ActiveProductionVersionMasterId = flow.ActiveProductionVersionMasterId;

            logger.debug(`Se actualiza la cache ${cacheKey}`);
            // Limpiar todas las cachés relacionadas para forzar regeneración
            if (myCache.has(cacheKey)) {
                myCache.del(cacheKey);
            }
            let stagingCacheKey = `${client}-${flowId}-staging`;
            if (myCache.has(stagingCacheKey)) {
                myCache.del(stagingCacheKey);
            }
            logger.debug(`[MASTERBOT] Cache limpiado para ${cacheKey} y ${stagingCacheKey}`);
            myCache.set(cacheKey, flowWithoutVersion);

            if (!flowWithoutVersion.isMasterModule) {
                this.getFlow(req, res, true);
            } else {
                if (sendRes) {
                    // Convertir a JSON
                    const jsonData = JSON.stringify(flowWithoutVersion);

                    // Calcular tamaño original en KB y MB
                    const originalSizeBytes = Buffer.byteLength(jsonData, 'utf8');
                    const originalSizeKB = (originalSizeBytes / 1024).toFixed(2);
                    const originalSizeMB = (originalSizeBytes / (1024 * 1024)).toFixed(2);

                    // Verificar si el cliente acepta respuestas comprimidas usando el encabezado personalizado
                    const acceptZip = req.headers['x-accept-zip'] === 'true';

                    // Si el cliente acepta zip y los datos son grandes (más de 800KB)
                    if (acceptZip && originalSizeBytes > 800 * 1024) {
                        logger.debug(`Comprimiendo respuesta grande con ZIP. Tamaño original: ${originalSizeMB} MB (${originalSizeKB} KB)`);
                        const JSZip = require('jszip');

                        // Comprimir con JSZip
                        const zip = new JSZip();
                        zip.file('data.json', jsonData);

                        zip.generateAsync({
                            type: 'nodebuffer',
                            compression: 'DEFLATE',
                            compressionOptions: {
                                level: 7
                            }
                        }).then(content => {
                            // Calcular tamaño comprimido en KB y MB
                            const compressedSizeBytes = content.length;
                            const compressedSizeKB = (compressedSizeBytes / 1024).toFixed(2);
                            const compressedSizeMB = (compressedSizeBytes / (1024 * 1024)).toFixed(2);

                            // Calcular ratio de compresión
                            const compressionRatio = ((1 - (compressedSizeBytes / originalSizeBytes)) * 100).toFixed(2);

                            logger.info(`Compresión completada. Tamaño comprimido: ${compressedSizeMB} MB (${compressedSizeKB} KB). Ratio de compresión: ${compressionRatio}%`);

                            // Enviar respuesta comprimida con los encabezados correctos
                            res.setHeader('Content-Type', 'application/zip');
                            res.setHeader('Content-Disposition', 'attachment; filename="flow.zip"');
                            res.setHeader('X-Content-Encoding', 'zip');
                            res.setHeader('X-Original-Size', originalSizeBytes.toString());
                            res.setHeader('X-Compressed-Size', compressedSizeBytes.toString());
                            res.setHeader('X-Compression-Ratio', compressionRatio.toString());
                            res.send(content);
                        }).catch(error => {
                            logger.error(`Error al comprimir respuesta: ${error}`);
                            // Si hay un error, enviar sin comprimir
                            res.setHeader('Content-Type', 'application/json');
                            res.send(flowWithoutVersion);
                        });
                    } else {
                        // Enviar respuesta sin comprimir
                        res.setHeader('Content-Type', 'application/json');
                        res.send(flowWithoutVersion);
                    }
                } else {
                    return flowWithoutVersion;
                }
            }
        }
    }

    async getFlowsBySocialService(req, res) {
        let includeModules = req.query.includeModules !== undefined ? req.query.includeModules.toLowerCase() === 'true' : false;
        let where = {
            [Op.or]: [{
                channel: req.params.channel
            }, {
                channel: SocialServiceType.Generic
            }],
            company_id: req.user.cid
        }
        if (!includeModules) {
            where.master_flow_id = {
                [Op.eq]: null
            };
        }
        var flows = await Flow.findAll({
            where: where,
            include: [
                {
                    model: FlowVersion,
                    as: 'ActiveStagingVersion',
                    attributes: {
                        exclude: ['blob', 'stats']
                    }
                },
                {
                    model: FlowVersion,
                    as: 'ActiveProductionVersion',
                    attributes: {
                        exclude: ['blob', 'stats']
                    }
                }
            ]
        });

        if (typeof (flows) !== 'undefined' &&
            flows !== null) {
            flows.sort((a, b) => {
                if (a.dataValues.name < b.dataValues.name) {
                    return -1;
                }
                else if (a.dataValues.name > b.dataValues.name) {
                    return 1;
                }
                return 0;
            });
        }

        return res.send(flows);
    }

    async getVersions(req, res) {
        var flow = await this.checkIfFlowExists(req, res, false);
        if (!flow) {
            return res.status(404).send({ success: false, message: 'Flow NOT FOUND' });
        }

        let limit = 10;   // number of records per page
        let page = parseInt(req.params.page);
        var data = await FlowVersion.findAndCountAll({
            attributes: ['id'],
            where: { flow_id: flow.id }
        });
        let pages = Math.ceil(data.count / limit);
        let offset = limit * (page - 1);

        var versions = await FlowVersion.findAll({
            attributes: ['id', 'number', 'created_at', 'flow_id', 'published_at', 'published_by_user_id', 'comments', 'user_id'],
            where: { flow_id: flow.id }, order: [['number', 'DESC']],
            include: [{
                model: UserAction,
                include: [{
                    model: User,
                    attributes: ['id', 'name']
                }]
            }, {
                model: User,
                attributes: ['id', 'name']
            }],
            limit: limit,
            offset: offset,
            $sort: { id: 1 }
        });
        return res.status(200).send({ flows: versions, pages: pages, current_page_number: page });
    }

    async getModules(req, res) {
        var flow = await this.checkIfFlowExists(req, res, false);
        if (!flow) {
            return res.status(404).send({ success: false, message: 'Flow NOT FOUND' });
        }
        let masterFlow = await Flow.findOneByIdWithoutVersion(flow.id, req.user.cid);
        let flowId = masterFlow.master_flow_id !== null ? masterFlow.master_flow_id : masterFlow.id;

        var flows = await Flow.findAllModulesById(flowId, req.user.cid, req.user.uid, false, false);

        return res.status(200).send({
            flows: flows.map(flow => {
                return {
                    id: flow.id,
                    name: flow.name,
                    activeStagingVersionId: flow.ActiveStagingVersionId,
                    activeProductionVersionId: flow.ActiveProductionVersionId,
                    activeProductionVersionMasterId: flow.ActiveProductionVersionMasterId,
                    activeStagingVersionNumber: typeof (flow.ActiveStagingVersion) === 'undefined' || flow.ActiveStagingVersion === null ? 0 : flow.ActiveStagingVersion.number,
                    activeProductionVersionNumber: typeof (flow.ActiveProductionVersion) === 'undefined' || flow.ActiveProductionVersion === null ? 0 : flow.ActiveProductionVersion.number,
                    activeProductionVersionMasterNumber: typeof (flow.ActiveProductionVersionMaster) === 'undefined' || flow.ActiveProductionVersionMaster === null ? 0 : flow.ActiveProductionVersionMaster.number
                };
            })
        });
    }

    async getChangeLog(req, res) {
        var flow = await this.checkIfFlowExists(req, res, false);
        if (!flow) {
            return res.status(404).send({ success: false, message: 'Flow NOT FOUND' });
        }

        var version = await FlowVersion.findOne({
            where: {
                id: req.query.flow_version_id
            },
            include: UserAction
        });

        if (version) {
            return res.status(200).send({ success: true, changes: version.user_action_logs });
        }
        else {
            return res.status(404).send({ success: false, message: 'NOT FOUND' });
        }
    }

    /**
     * Maneja la recepción de un fragmento de flujo para creación
     * @param {Object} req Solicitud HTTP
     * @param {Object} res Respuesta HTTP
     * @returns {Object} Respuesta HTTP
     */
    async createFlowChunk(req, res) {
        // Verificar que exista el flujo
        var flow = await Flow.findOne({
            where: {
                id: req.params.id
            }
        });

        if (!flow) {
            return res.status(404).send({ success: false, message: 'Flow NOT FOUND' });
        }

        // Obtener datos del fragmento
        const { uploadId, hash, totalChunks, chunk } = req.body;

        if (!uploadId || hash === undefined || !totalChunks || !chunk) {
            return res.status(400).send({ success: false, message: 'Faltan parámetros requeridos' });
        }

        try {
            // Crear directorio temporal si no existe
            const tempDir = `${contextFolder}/temp/${uploadId}`;
            if (!fs.existsSync(tempDir)) {
                fs.mkdirSync(tempDir, { recursive: true });
            }

            // Guardar el fragmento en un archivo temporal
            const chunkPath = `${tempDir}/${hash}`;
            fs.writeFileSync(chunkPath, chunk);

            logger.debug(`[CHUNKING] Fragmento ${hash + 1}/${totalChunks} recibido y guardado en ${chunkPath}`);

            return res.status(200).send({
                success: true,
                message: `Fragmento ${hash + 1}/${totalChunks} recibido correctamente`,
                hash: hash,
                totalChunks: totalChunks
            });
        } catch (error) {
            logger.error(`[ERROR] Error al procesar fragmento: ${error}`);
            return res.status(500).send({ success: false, message: 'Error al procesar fragmento' });
        }
    }

    /**
     * Fusiona todos los fragmentos de un flujo y lo guarda en la versión correspondiente
     * @param {Object} req Solicitud HTTP
     * @param {Object} res Respuesta HTTP
     * @returns {Object} Respuesta HTTP
     */
    async createFlowMerge(req, res) {
        // Verificar que exista el flujo
        var flow = await Flow.findOne({
            where: {
                id: req.params.id,
                company_id: req.user.cid
            }
        });

        if (!flow) {
            return res.status(404).send({ success: false, message: 'Flow NOT FOUND' });
        }

        // Obtener datos de la solicitud
        const { uploadId, totalChunks } = req.body;

        if (!uploadId || !totalChunks) {
            return res.status(400).send({ success: false, message: 'Faltan parámetros requeridos' });
        }

        // Directorio temporal para los fragmentos
        const tempDir = `${contextFolder}/temp/${uploadId}`;
        const combinedFilePath = `${tempDir}/combined.zip`;
        const decompressedFilePath = `${tempDir}/decompressed.json`;

        try {
            // Verificar que todos los fragmentos estén presentes
            let allChunksPresent = true;
            for (let i = 0; i < totalChunks; i++) {
                const chunkPath = `${tempDir}/${i}`;
                if (!fs.existsSync(chunkPath)) {
                    allChunksPresent = false;
                    logger.error(`[ERROR] Falta el fragmento ${i + 1}/${totalChunks}`);
                    break;
                }
            }

            if (!allChunksPresent) {
                return res.status(400).send({ success: false, message: 'Faltan fragmentos para completar la carga' });
            }

            logger.debug(`[CHUNKING] Iniciando fusión de ${totalChunks} fragmentos usando streams...`);

            // Crear un stream de escritura para el archivo combinado
            const writeStream = fs.createWriteStream(combinedFilePath);

            // Procesar cada chunk como stream secuencialmente para evitar sobrecarga de memoria
            for (let i = 0; i < totalChunks; i++) {
                const chunkPath = `${tempDir}/${i}`;

                // Usar promesas para manejar el streaming de manera asíncrona
                await new Promise((resolve, reject) => {
                    logger.debug(`[CHUNKING] Procesando fragmento ${i + 1}/${totalChunks}`);
                    const chunkStream = fs.createReadStream(chunkPath);

                    chunkStream.on('error', (error) => {
                        logger.error(`[ERROR] Error al leer fragmento ${i}: ${error}`);
                        reject(error);
                    });

                    // Pipe el contenido al stream combinado, pero no cerrar el writeStream todavía
                    chunkStream.pipe(writeStream, { end: false });

                    // Cuando termine este chunk, resolver la promesa
                    chunkStream.on('end', () => {
                        logger.debug(`[CHUNKING] Fragmento ${i + 1}/${totalChunks} procesado`);
                        resolve();
                    });
                });
            }

            // Cerrar el stream de escritura y esperar a que termine
            await new Promise((resolve, reject) => {
                writeStream.end();
                writeStream.on('finish', () => {
                    logger.debug(`[CHUNKING] Todos los fragmentos combinados en archivo temporal`);
                    resolve();
                });
                writeStream.on('error', reject);
            });

            // Obtener estadísticas del archivo combinado
            const stats = fs.statSync(combinedFilePath);
            logger.debug(`[CHUNKING] Tamaño del archivo combinado: ${Math.round(stats.size / 1024)} KB`);

            // Descomprimir el archivo combinado usando streams
            logger.debug(`[DESCOMPRESIÓN] Iniciando descompresión del archivo combinado...`);

            // Leer el archivo combinado como stream para JSZip
            const zipBuffer = fs.readFileSync(combinedFilePath);

            // Descomprimir usando JSZip
            const JSZip = require('jszip');
            const zip = await JSZip.loadAsync(zipBuffer);

            // Obtener el primer archivo
            const files = Object.keys(zip.files);
            if (files.length === 0) {
                return res.status(400).send({ success: false, message: 'Archivo ZIP vacío' });
            }

            // Extraer el contenido a un archivo temporal en lugar de mantenerlo en memoria
            const content = await zip.files[files[0]].async('string');
            fs.writeFileSync(decompressedFilePath, content);

            logger.debug(`[DESCOMPRESIÓN] Contenido descomprimido y guardado en archivo temporal`);

            // Leer el contenido del archivo temporal
            const flowContent = fs.readFileSync(decompressedFilePath, 'utf8');

            // Validar que el contenido sea un JSON válido
            try {
                JSON.parse(flowContent);
                logger.debug(`[VALIDACIÓN] JSON validado correctamente para creación`);
            } catch (jsonError) {
                logger.error(`[ERROR] Error al parsear JSON: ${jsonError.message}`);
                logger.error(`[ERROR] Primeros 200 caracteres del contenido: ${flowContent.substring(0, 200)}`);

                // Limpiar archivos temporales
                this.cleanupTempFiles(tempDir);

                return res.status(403).send({ success: false, message: 'El contenido del archivo es inválido' });
            }

            // Crear una nueva versión con el contenido
            logger.debug(`[CHUNKING] Creando nueva versión del flujo...`);
            var version = await FlowVersion.createNewVersion(flowContent, flow.id, null, req.user.uid, req.user.cid);
            await Flow.update({ ActiveStagingVersionId: version.id }, { where: { id: flow.id } });

            // Limpiar archivos temporales
            logger.debug(`[CHUNKING] Limpiando archivos temporales...`);
            this.cleanupTempFiles(tempDir);

            // Registrar la acción en auditoría
            const userLocale = this.getUserLocale(req);
            auditService.registrarCreacion(
                req.user.name,
                EntityTypes.FLUJO.name,
                flow.id.toString(),
                flow.name,
                null,
                userLocale
            ).catch(error => {
                logger.error(`Error al registrar auditoría de creación de flujo: ${error}`);
            });

            return res.status(200).send({
                success: true,
                message: 'Flujo creado correctamente',
                id: flow.id,
                versionId: version.id,
                versionNumber: version.number
            });
        } catch (error) {
            logger.error(`[ERROR] Error al fusionar fragmentos: ${error}`);

            // Intentar limpiar archivos temporales en caso de error
            try {
                this.cleanupTempFiles(tempDir);
            } catch (cleanupError) {
                logger.error(`[ERROR] Error al limpiar archivos temporales: ${cleanupError}`);
            }

            return res.status(500).send({ success: false, message: 'Error al fusionar fragmentos' });
        }
    }

    async createFlow(req, res) {
        const t = await app.sequelize.transaction();
        try {
            let flowExists = await Flow.findOne({ where: { name: req.body.name } })
            if (flowExists) {
                await t.rollback();
                if (flowExists.master_flow_id) {
                    let masterExists = await Flow.findOne({ where: { id: flowExists.master_flow_id } })
                    return res.status(500).send({ success: false, message: localeServices.translate("MODULE_EXISTS", { moduleName: flowExists.name, masterName: masterExists.name }) });
                }
                return res.status(500).send({ success: false, message: localeServices.translate("MASTER_EXISTS", { flowName: flowExists.name }) });
            }

            // Verificar si es una solicitud de carga en fragmentos
            if (req.body.isChunked) {
                logger.debug(`[CHUNKING] Creando flujo para carga en fragmentos: ${req.body.name}`);

                // Crear el flujo sin blob para luego actualizarlo con los fragmentos
                var newFlow = await Flow.create({
                    name: req.body.name,
                    user_id: req.user.uid,
                    channel: req.body.channel,
                    type: req.body.type,
                    company_id: req.user.cid,
                    master_flow_id: req.body.masterId
                }, { transaction: t });

                await t.commit();

                return res.status(201).send({
                    success: true,
                    message: 'Flow created for chunked upload',
                    data: {
                        id: newFlow.id,
                        name: newFlow.name
                    }
                });
            }

            var flow = await Flow.create({
                name: req.body.name,
                user_id: req.user.uid,
                channel: req.body.channel,
                type: req.body.type,
                company_id: req.user.cid,
                master_flow_id: req.body.masterId
            });

            if (!flow) {
                return res.status(500).send({ success: false, message: localeServices.translate("FLOW_CREATE_ERROR", { name: req.body.name }) });
            }

            req.body.id = flow.id;
            var permissions = await this.addFlowPermissions(req);
            await this.saveFlow(req, res, false);

            const userLocale = this.getUserLocale(req);

            // Registrar la creación del flujo en auditoría
            auditService.registrarCreacion(
                req.user.name,
                EntityTypes.FLUJO.name,
                flow.id.toString(),
                req.body.name,
                auditTranslations.getValueTranslation('flow_created'),
                null,
                userLocale
            ).catch(error => {
                logger.error(`Error al registrar auditoría de creación de flujo: ${error}`);
            });

            if (res.statusMessage === undefined) {
                t.commit();
                return res.status(201).send({ success: true, message: 'Flow created', data: { id: flow.id, permissions: permissions } });
            }
            else if (flow) {
                await Flow.destroy({
                    where: {
                        id: flow.id
                    }
                });
                await t.rollback();
                return res.status(500).send({ success: true, message: `error in create flows: ${res.statusMessage}` });
            }
        }
        catch (error) {
            if (flow) {
                await Flow.destroy({
                    where: {
                        id: flow.id
                    }
                });
                await t.rollback();
            }
            logger.error(`Error en la creación de flujo, error: ${error}`);
            return res.status(500).send({ success: false, message: 'error in create flows' });
        }
    }

    async addFlowPermissions(req) {
        var users = await User.findAll({
            attributes: ['id', 'can_edit', 'can_publish', 'can_see_statistics', 'can_access_ysmart'],
            where: {
                company_id: req.user.cid
            }
        });
        var permissions = [];
        if (typeof (users) !== 'undefined' && users.length > 0) {
            for (let i = 0; i < users.length; i++) {
                permissions.push({
                    userId: users[i].id,
                    canEdit: users[i].can_edit,
                    canPublish: users[i].can_publish,
                    canSeeStatistics: users[i].can_see_statistics,
                    canAccessYSmart: users[i].can_access_ysmart
                });
                await UserPermissionFlow.create({
                    userId: users[i].id,
                    flowId: req.body.id,
                    canEdit: users[i].can_edit,
                    canPublish: users[i].can_publish,
                    canSeeStatistics: users[i].can_see_statistics,
                    canAccessYSmart: users[i].can_access_ysmart
                });
            }
        }
        return permissions;
    }
    async checkIfFlowExists(req, res, includeVersions = true) {
        if (req.query.id == null && req.params.id == null) {
            return res.status(400).send({ success: false, message: 'id is missing' });
        }

        const id = req.query.id ? req.query.id : req.params.id;
        let flow = includeVersions ? await Flow.findOneById(id, req.user.cid, req.user.uid) : await Flow.findOneByIdWithoutVersion(id, req.user.cid);

        if (!flow) {
            return res.status(404).send({ success: false, message: 'Flow NOT FOUND' });
        }

        return flow;
    }
    async saveFlow(req, res, sendRes = true) {
        let flowId = req.body.id;
        var existing = await Flow.findOne({
            where: {
                name: req.body.name,
                company_id: req.user.cid,
                deleted_at: null,
                id: { [Op.ne]: flowId }
            }
        });

        if (existing) {
            return res.status(403).send({ success: false, message: 'Ya existe un flow con ese nombre' });
        }

        // Obtener el flujo actual para detectar cambios
        const currentFlow = await Flow.findOne({
            where: { id: flowId },
            attributes: ['name']
        });

        const nameChanged = currentFlow && currentFlow.name !== req.body.name;

        var rowsChanged = await Flow.update({
            name: req.body.name
        }, {
            where: { id: flowId }
        });

        if (sendRes && rowsChanged[0] === 0) {
            return res.status(404).send({ success: false, message: 'Flow NOT FOUND' });
        }

        // Obtener el idioma del usuario
        const userLocale = this.getUserLocale(req);

        // Si el nombre ha cambiado, registrar esto en auditoría
        if (nameChanged) {
            auditService.registrarEdicion(
                req.user.name,
                EntityTypes.FLUJO.name,
                flowId.toString(),
                req.body.name,
                auditTranslations.getPropertyTranslation(EntityTypes.FLUJO.name, 'nombre'),
                req.body.name,
                currentFlow.name,
                null,
                userLocale
            ).catch(error => {
                logger.error(`Error al registrar auditoría de cambio de nombre de flujo: ${error}`);
            });
        }

        // Verificar si hay datos para guardar (blob o zipData)
        if (req.body.blob || (req.headers['x-content-encoding'] === 'zip' && req.body.zipData)) {
            let blobToSave;

            // Si los datos están comprimidos, descomprimirlos
            if (req.headers['x-content-encoding'] === 'zip' && req.body.zipData) {
                // Calcular tamaño comprimido en KB
                const compressedSizeKB = Math.round((req.body.zipData.length * 0.75) / 1024); // Aproximación base64 a binario
                logger.debug(`[DESCOMPRESIÓN] Iniciando descompresión de flujo. Tamaño comprimido: ${compressedSizeKB} KB`);

                const JSZip = require('jszip');
                try {
                    // Descomprimir los datos
                    const zip = await JSZip.loadAsync(Buffer.from(req.body.zipData, 'base64'));

                    // Obtener el primer archivo
                    const files = Object.keys(zip.files);
                    if (files.length === 0) {
                        return res.status(400).send({ success: false, message: 'Archivo ZIP vacío' });
                    }

                    // Leer el contenido del archivo
                    blobToSave = await zip.files[files[0]].async('string');

                    // Calcular tamaño descomprimido en KB
                    const decompressedSizeKB = Math.round(blobToSave.length / 1024);
                    const compressionRatio = Math.round((1 - (compressedSizeKB / decompressedSizeKB)) * 100);
                    logger.debug(`[DESCOMPRESIÓN] Descompresión completada. Tamaño descomprimido: ${decompressedSizeKB} KB. Ratio: ${compressionRatio}%`);
                } catch (error) {
                    logger.error(`Error al descomprimir datos: ${error}`);
                    return res.status(400).send({ success: false, message: 'Error al descomprimir datos' });
                }
            } else {
                blobToSave = req.body.blob;
            }

            let newVersion = await FlowVersion.createNewVersion(
                blobToSave,
                flowId,
                req.body.comments,
                req.user.uid,
                req.user.cid
            );

            if (!newVersion) {
                return res.status(400).send({ success: false, message: 'Error durante la creación del flujo' });
            }

            let flow = await Flow.findOneById(flowId, req.user.cid, req.user.uid);

            if (flow.ActiveStagingVersion != null) {
                var changes = DiffHelper.generateDiff(blobToSave, flow.ActiveStagingVersion.blob);
                await UserAction.bulkCreate(changes.map(change => ({
                    action: typeof (change) === 'object' ? JSON.stringify(change) : change,
                    user_id: req.user.uid,
                    flow_version_id: newVersion.id
                })));
            }

            await Flow.update({
                ActiveStagingVersionId: newVersion.id,
                user: req.user.uid,
            }, {
                where: { id: flowId }
            });

            if (!nameChanged) {
                // Si no hay versión activa, es la primera versión, registrar como creación
                if (!flow.ActiveStagingVersion) {
                    auditService.registrarCreacion(
                        req.user.name,
                        EntityTypes.FLUJO.name,
                        flowId.toString(),
                        req.body.name,
                        auditTranslations.getPropertyTranslation(EntityTypes.FLUJO.name, 'version', null, userLocale) + ': ' +
                        auditTranslations.formatValue(EntityTypes.FLUJO.name, 'version', newVersion.number, null, userLocale),
                        null,
                        userLocale
                    ).catch(error => {
                        logger.error(`Error al registrar auditoría de creación de primera versión de flujo: ${error}`);
                    });
                } else {
                    // Si ya hay una versión activa, es una edición
                    auditService.registrarEdicion(
                        req.user.name,
                        EntityTypes.FLUJO.name,
                        flowId.toString(),
                        req.body.name,
                        auditTranslations.getPropertyTranslation(EntityTypes.FLUJO.name, 'version', null, userLocale),
                        auditTranslations.formatValue(EntityTypes.FLUJO.name, 'version', newVersion.number, null, userLocale),
                        auditTranslations.formatValue(EntityTypes.FLUJO.name, 'version', flow.ActiveStagingVersion.number, null, userLocale),
                        null,
                        userLocale
                    ).catch(error => {
                        logger.error(`Error al registrar auditoría de guardado de flujo: ${error}`);
                    });
                }
            }

            // ... código existente de cache
            let flowIdToUpdateCache = flow.master_flow_id || flow.id;
            let cacheKeys = [
                `${client}-${flowIdToUpdateCache}-staging`,
                `${client}-${flowIdToUpdateCache}-tester`
            ];

            cacheKeys.forEach(cacheKey => {
                if (redis !== null) {
                    redis.sessionClient.publish(client, JSON.stringify({
                        action: 'saving',
                        flow: flowIdToUpdateCache,
                        cacheKey: cacheKey
                    }));
                }
                logger.info(`Se actualiza la cache ${cacheKey}`);
                if (myCache.has(cacheKey)) {
                    myCache.del(cacheKey);
                }
                myCache.set(cacheKey, flow);
            });
        } else if (sendRes) {
            return res.status(404).send({ success: false, message: 'blob missing' });
        }

        if (sendRes) {
            return res.status(200).send({ success: true, message: 'Flow actualizado' });
        }
    }

    async publishFlow(req, res) {
        let flow = await this.checkIfFlowExists(req, res);
        if (!flow) {
            return res.status(404).send({ success: false, message: 'Flow NOT FOUND' });
        }

        const onlyIntegrations = req.body && req.body.onlyIntegrations;

        // Obtener el idioma del usuario
        const userLocale = req.session && req.session.locale ? req.session.locale :
            req.cookies && req.cookies.locale ? req.cookies.locale :
                req.headers['accept-language'] ? req.headers['accept-language'].split(',')[0].substring(0, 2) : 'es';

        if (onlyIntegrations) {
            if (!flow.ActiveProductionVersion) {
                return res.status(400).send({
                    success: false,
                    message: 'No existe versión en producción para actualizar integraciones'
                });
            }

            const incomingBlob = JSON.parse(req.body.blob);
            if (!incomingBlob.integrations) {
                return res.status(400).send({
                    success: false,
                    message: 'No se encontraron integraciones en el flujo'
                });
            }

            let currentProductionBlob = JSON.parse(flow.ActiveProductionVersion.blob);

            if (currentProductionBlob && currentProductionBlob.IntegrationDefinitions) {
                currentProductionBlob.IntegrationDefinitions.forEach(integration => {
                    const updatedIntegration = incomingBlob.integrations.find(i => i.id === integration.id);
                    if (updatedIntegration) {
                        integration.enabled = updatedIntegration.enabled;
                    }
                });
            }

            let newVersionNumber = flow.ActiveProductionVersion.number + 0.1;
            let newVersion = await FlowVersion.createNewVersion(
                JSON.stringify(currentProductionBlob),
                flow.id,
                "Actualización de integraciones",
                req.user.uid,
                req.user.cid,
                newVersionNumber
            );

            flow.ActiveProductionVersion = newVersion;
            flow.ActiveProductionVersionId = newVersion.id;
            flow.ActiveProductionVersion.published_at = moment().toDate();
            flow.ActiveProductionVersion.published_by_user_id = req.user.uid;
            await flow.save();

            let modules = await Flow.findAllModulesById(flow.id, req.user.cid, req.user.uid);
            for (const module of modules) {
                module.ActiveProductionVersionMasterId = module.ActiveProductionVersionId;
                await module.save();
            }

            let cacheKeys = [];

            if (flow.master_flow_id !== null) {
                cacheKeys = [
                    `${client}-${flow.master_flow_id}-staging`,
                ];
            } else {
                cacheKeys = [
                    `${client}-${flow.id}-prod`,
                    `${client}-${flow.id}-staging`
                ];
            }

            cacheKeys.forEach(cacheKey => {
                if (redis !== null) {
                    redis.sessionClient.publish(client, JSON.stringify({
                        action: 'publish',
                        flow: flow.id,
                        cacheKey: cacheKey
                    }), function (error) {
                        if (error !== null) {
                            logger.info(`Hubo un error al querer publicar en redis el flow ${flow.id}-${flow.name}: ${error}`);
                        } else {
                            logger.info(`Se publicó correctamente en redis el flow ${flow.id}-${flow.name}`);
                        }
                    });
                } else if (myCache.has(cacheKey)) {
                    myCache.del(cacheKey);
                }
            });

            if (!standAlone) {
                await this.notifyYSocial(flow, JSON.stringify(currentProductionBlob));
            }

            // Obtener el idioma del usuario
            const userLocale = this.getUserLocale(req);

            // Registrar publicación de integraciones en auditoría
            auditService.registrarEdicion(
                req.user.name,
                EntityTypes.FLUJO.name,
                flow.id.toString(),
                flow.name,
                auditTranslations.getPropertyTranslation(EntityTypes.FLUJO.name, 'integraciones', null, userLocale),
                auditTranslations.getValueTranslation('integrations_updated', null, userLocale),
                auditTranslations.getValueTranslation('previous_integrations', null, userLocale),
                null,
                userLocale
            ).catch(error => {
                logger.error(`Error al registrar auditoría de publicación de integraciones: ${error}`);
            });

            return res.status(200).send({
                success: true,
                message: localeServices.translate("FLOW_PUBLISHED")
            });
        } else {
            // ... código existente de publicación normal
            flow.ActiveStagingVersion.published_at = moment().toDate();
            flow.ActiveStagingVersion.published_by_user_id = req.user.uid;
            await flow.ActiveStagingVersion.save();
            let blob = flow.ActiveStagingVersion.blob;

            flow.ActiveProductionVersionId = flow.ActiveStagingVersionId;
            await flow.save();

            let modules = await Flow.findAllModulesById(flow.id, req.user.cid, req.user.uid);
            modules.forEach(async module => {
                module.ActiveProductionVersionMasterId = module.ActiveProductionVersionId;
                await module.save();
            });

            let cacheKey;
            if (flow.master_flow_id !== null) {
                cacheKey = `${client}-${flow.master_flow_id}-staging`;
            } else {
                cacheKey = `${client}-${flow.id}-prod`;
            }

            //ToDo: Migrar esto a una funcion para evitar repetir este codigo
            if (redis !== null) {
                redis.sessionClient.publish(client, JSON.stringify({
                    action: 'publish',
                    flow: flow.id,
                    cacheKey: cacheKey
                }), function (error) {
                    if (error !== null) {
                        logger.info(`Hubo un error al querer publicar en redis el flow ${flow.id}-${flow.name}: ${error}`);
                    } else {
                        logger.info(`Se publicó correctamente en redis el flow ${flow.id}-${flow.name}`);
                    }
                });
            } else if (myCache.has(cacheKey)) {
                myCache.del(cacheKey);
            }

            if (!standAlone) {
                await this.notifyYSocial(flow, blob);
            }

            // Registrar publicación de flujo en auditoría
            const previousVersion = await FlowVersion.findOne({
                where: {
                    flow_id: flow.id,
                    id: { [Op.ne]: flow.ActiveStagingVersionId },
                    published_at: { [Op.ne]: null }
                },
                order: [['published_at', 'DESC']]
            });

            auditService.registrarEdicion(
                req.user.name,
                EntityTypes.FLUJO.name,
                flow.id.toString(),
                flow.name,
                auditTranslations.getPropertyTranslation(EntityTypes.FLUJO.name, 'publicacion'),
                auditTranslations.formatValue(EntityTypes.FLUJO.name, 'version_publicada', flow.ActiveStagingVersion.number),
                auditTranslations.formatValue(EntityTypes.FLUJO.name, 'version_anterior', previousVersion ? previousVersion.number : 'N/A'),
                null,
                userLocale
            ).catch(error => {
                logger.error(`Error al registrar auditoría de publicación de flujo: ${error}`);
            });

            return res.status(200).send({
                success: true,
                message: 'Flow publicado'
            });
        }
    }

    async notifyYSocial(flow, blob) {
        logger.info(`Se invocará a ySocial para informar de la publicación del flow ${flow.id}-${flow.name}`);

        let url = process.env.ySocialUrl;
        try {
            let flowDefinition = JSON.parse(blob);
            if (typeof (flowDefinition.YSocialSettings) !== 'undefined' &&
                flowDefinition.YSocialSettings !== null &&
                typeof (flowDefinition.YSocialSettings.Url) !== 'undefined' &&
                flowDefinition.YSocialSettings.Url !== null) {
                url = flowDefinition.YSocialSettings.Url;
            }
        } catch (e) {
            logger.error(`No se pudo leer la configuración de ySocial del flow: ${e}`);
        }

        if (!url.endsWith('/')) {
            url += '/';
        }
        url += 'services/flow/publish?id=' + flow.id;

        try {
            let requestOptionsCA;
            if (fs.existsSync('ssl/request_ca_certs.pem')) {
                requestOptionsCA = [fs.readFileSync('ssl/request_ca_certs.pem')];
                logger.info(`Se utilizará los certificados certificantes desde ssl/request_ca_certs.pem`);
            }

            const httpsAgent = new https.Agent({
                rejectUnauthorized: false,
                ca: requestOptionsCA
            });

            await axios({
                method: 'POST',
                url: url,
                data: '',
                headers: authorization('POST', url),
                httpsAgent: httpsAgent
            });

            logger.info(`Se informó a ySocial de la publicación del flow ${flow.id}-${flow.name}`);
        } catch (e) {
            logger.info(`Se produjo un error invocando a ySocial para informar de la publicación del flow ${e}`);
        }
    }

    /**
     * Maneja la recepción de un fragmento de flujo para restauración
     * @param {Object} req Solicitud HTTP
     * @param {Object} res Respuesta HTTP
     * @returns {Object} Respuesta HTTP
     */
    async restoreFlowChunk(req, res) {
        // Obtener datos del fragmento
        const { uploadId, hash, totalChunks, chunk, flowId, versionId } = req.body;

        if (!uploadId || hash === undefined || !totalChunks || !chunk || !flowId || !versionId) {
            return res.status(400).send({ success: false, message: 'Faltan parámetros requeridos' });
        }

        try {
            // Verificar que exista el flujo
            var flow = await Flow.findOne({
                where: {
                    id: flowId,
                    company_id: req.user.cid
                }
            });

            if (!flow) {
                return res.status(404).send({ success: false, message: 'Flow NOT FOUND' });
            }

            // Verificar que exista la versión
            var version = await FlowVersion.findOne({
                where: {
                    id: versionId
                }
            });

            if (!version) {
                return res.status(404).send({ success: false, message: 'Version NOT FOUND' });
            }

            // Crear directorio temporal si no existe
            const tempDir = `${contextFolder}/temp/${uploadId}`;
            if (!fs.existsSync(tempDir)) {
                fs.mkdirSync(tempDir, { recursive: true });

                // Guardar información del flujo en un archivo JSON
                const flowInfoPath = `${tempDir}/flow_info.json`;
                fs.writeFileSync(flowInfoPath, JSON.stringify({
                    flowId: flowId,
                    versionId: versionId,
                    user_id: req.user.uid,
                    company_id: req.user.cid
                }));
            }

            // Guardar el fragmento en un archivo temporal
            const chunkPath = `${tempDir}/${hash}`;
            fs.writeFileSync(chunkPath, chunk);

            logger.debug(`[CHUNKING] Fragmento ${hash + 1}/${totalChunks} recibido y guardado en ${chunkPath}`);

            return res.status(200).send({
                success: true,
                message: `Fragmento ${hash + 1}/${totalChunks} recibido correctamente`,
                hash: hash,
                totalChunks: totalChunks
            });
        } catch (error) {
            logger.error(`[ERROR] Error al procesar fragmento: ${error}`);
            return res.status(500).send({ success: false, message: 'Error al procesar fragmento' });
        }
    }

    /**
     * Fusiona todos los fragmentos de un flujo y lo restaura
     * @param {Object} req Solicitud HTTP
     * @param {Object} res Respuesta HTTP
     * @returns {Object} Respuesta HTTP
     */
    async restoreFlowMerge(req, res) {
        // Obtener datos de la solicitud
        const { uploadId, totalChunks } = req.body;

        if (!uploadId || !totalChunks) {
            return res.status(400).send({ success: false, message: 'Faltan parámetros requeridos' });
        }

        // Directorio temporal para los fragmentos
        const tempDir = `${contextFolder}/temp/${uploadId}`;
        const flowInfoPath = `${tempDir}/flow_info.json`;
        const combinedFilePath = `${tempDir}/combined.zip`;
        const decompressedFilePath = `${tempDir}/decompressed.json`;

        try {
            // Verificar que exista la información del flujo
            if (!fs.existsSync(flowInfoPath)) {
                return res.status(400).send({ success: false, message: 'Falta información del flujo' });
            }

            const flowInfo = JSON.parse(fs.readFileSync(flowInfoPath, 'utf8'));
            const { flowId, versionId } = flowInfo;

            // Verificar que exista el flujo
            var flow = await Flow.findOne({
                where: {
                    id: flowId,
                    company_id: req.user.cid
                }
            });

            if (!flow) {
                return res.status(404).send({ success: false, message: 'Flow NOT FOUND' });
            }

            // Verificar que exista la versión
            var version = await FlowVersion.findOne({
                where: {
                    id: versionId
                }
            });

            if (!version) {
                return res.status(404).send({ success: false, message: 'Version NOT FOUND' });
            }

            // Verificar que todos los fragmentos estén presentes
            let allChunksPresent = true;
            for (let i = 0; i < totalChunks; i++) {
                const chunkPath = `${tempDir}/${i}`;
                if (!fs.existsSync(chunkPath)) {
                    allChunksPresent = false;
                    logger.error(`[ERROR] Falta el fragmento ${i + 1}/${totalChunks}`);
                    break;
                }
            }

            if (!allChunksPresent) {
                return res.status(400).send({ success: false, message: 'Faltan fragmentos para completar la carga' });
            }

            logger.debug(`[CHUNKING] Iniciando fusión de ${totalChunks} fragmentos usando streams...`);

            // Crear un stream de escritura para el archivo combinado
            const writeStream = fs.createWriteStream(combinedFilePath);

            // Procesar cada chunk como stream secuencialmente para evitar sobrecarga de memoria
            for (let i = 0; i < totalChunks; i++) {
                const chunkPath = `${tempDir}/${i}`;

                // Usar promesas para manejar el streaming de manera asíncrona
                await new Promise((resolve, reject) => {
                    logger.debug(`[CHUNKING] Procesando fragmento ${i + 1}/${totalChunks}`);
                    const chunkStream = fs.createReadStream(chunkPath);

                    chunkStream.on('error', (error) => {
                        logger.error(`[ERROR] Error al leer fragmento ${i}: ${error}`);
                        reject(error);
                    });

                    // Pipe el contenido al stream combinado, pero no cerrar el writeStream todavía
                    chunkStream.pipe(writeStream, { end: false });

                    // Cuando termine este chunk, resolver la promesa
                    chunkStream.on('end', () => {
                        logger.debug(`[CHUNKING] Fragmento ${i + 1}/${totalChunks} procesado`);
                        resolve();
                    });
                });
            }

            // Cerrar el stream de escritura y esperar a que termine
            await new Promise((resolve, reject) => {
                writeStream.end();
                writeStream.on('finish', () => {
                    logger.debug(`[CHUNKING] Todos los fragmentos combinados en archivo temporal`);
                    resolve();
                });
                writeStream.on('error', reject);
            });

            // Obtener estadísticas del archivo combinado
            const stats = fs.statSync(combinedFilePath);
            logger.debug(`[CHUNKING] Tamaño del archivo combinado: ${Math.round(stats.size / 1024)} KB`);

            // Descomprimir el archivo combinado
            logger.debug(`[DESCOMPRESIÓN] Iniciando descompresión del archivo combinado...`);

            // Leer el archivo combinado como buffer
            const combinedBuffer = fs.readFileSync(combinedFilePath);

            let decompressedContent = null;

            try {
                // Intentar múltiples métodos de descompresión en secuencia
                const base64Start = combinedBuffer.toString('ascii', 0, 10);

                // 1. Intentar descomprimir con zlib directamente (formato gzip)
                try {
                    decompressedContent = zlib.unzipSync(combinedBuffer).toString('utf8');
                    logger.debug(`[DESCOMPRESIÓN] Descompresión con zlib exitosa`);
                } catch (gzipError) {
                    // 2. Verificar si podría ser base64 de gzip (comienza con H4sI)
                    if (base64Start.startsWith('H4sI')) {
                        try {
                            const binaryData = Buffer.from(combinedBuffer.toString('ascii'), 'base64');
                            decompressedContent = zlib.unzipSync(binaryData).toString('utf8');
                            logger.debug(`[DESCOMPRESIÓN] Descompresión de base64+gzip exitosa`);
                        } catch (base64GzipError) {
                            // Continuar con el siguiente método
                        }
                    }

                    // 2.1 Verificar si podría ser base64 de ZIP (comienza con UEsD)
                    if (!decompressedContent && (base64Start.startsWith('UEsD') || base64Start.startsWith('UEsDB'))) {
                        try {
                            const binaryData = Buffer.from(combinedBuffer.toString('ascii'), 'base64');
                            const JSZip = require('jszip');
                            const zip = await JSZip.loadAsync(binaryData);

                            // Obtener el primer archivo
                            const files = Object.keys(zip.files);
                            if (files.length === 0) {
                                throw new Error('Archivo ZIP vacío');
                            }

                            // Extraer el contenido
                            decompressedContent = await zip.files[files[0]].async('string');
                            logger.debug(`[DESCOMPRESIÓN] Descompresión de base64+ZIP exitosa`);
                        } catch (base64ZipError) {
                            // Continuar con el siguiente método
                        }
                    }

                    // 3. Si aún no se ha descomprimido, intentar con JSZip
                    if (!decompressedContent) {
                        try {
                            const JSZip = require('jszip');
                            const zip = await JSZip.loadAsync(combinedBuffer);

                            // Obtener el primer archivo
                            const files = Object.keys(zip.files);
                            if (files.length === 0) {
                                throw new Error('Archivo ZIP vacío');
                            }

                            // Extraer el contenido
                            decompressedContent = await zip.files[files[0]].async('string');
                            logger.debug(`[DESCOMPRESIÓN] Descompresión con JSZip exitosa`);
                        } catch (zipError) {
                            // Si llegamos aquí, ningún método funcionó
                            throw new Error(`No se pudo descomprimir el archivo con ningún método: ${zipError.message}`);
                        }
                    }
                }

                // 4. Verificar que el contenido se haya descomprimido correctamente
                if (!decompressedContent) {
                    throw new Error('No se pudo descomprimir el contenido con ningún método');
                }

                // Guardar el contenido descomprimido en un archivo temporal
                fs.writeFileSync(decompressedFilePath, decompressedContent);
                logger.debug(`[DESCOMPRESIÓN] Contenido descomprimido y guardado en archivo temporal`);
            } catch (error) {
                logger.error(`[ERROR] Error al descomprimir datos: ${error}`);
                throw error;
            }

            // Leer el contenido del archivo temporal
            const flowContent = fs.readFileSync(decompressedFilePath, 'utf8');

            // Validar que el contenido sea un JSON válido
            try {
                JSON.parse(flowContent);
                logger.debug(`[VALIDACIÓN] JSON validado correctamente para restauración`);
            } catch (jsonError) {
                logger.error(`[ERROR] Error al parsear JSON: ${jsonError.message}`);
                logger.error(`[ERROR] Primeros 200 caracteres del contenido: ${flowContent.substring(0, 200)}`);

                // Limpiar archivos temporales
                this.cleanupTempFiles(tempDir);

                return res.status(403).send({ success: false, message: 'El contenido del archivo es inválido' });
            }

            // Crear una nueva versión con el contenido
            logger.debug(`[CHUNKING] Creando nueva versión del flujo...`);
            var newVersion = await FlowVersion.createNewVersion(flowContent, flow.id, null, req.user.uid, req.user.cid);
            await Flow.update({ ActiveStagingVersionId: newVersion.id }, { where: { id: flow.id } });

            // Registrar la acción si hay una versión de staging
            if (flow.ActiveStagingVersionId) {
                let action = {
                    t: DiffHelper.changeType.FLOW_RESTORED,
                    v: version.number
                };
                await UserAction.create({
                    user_id: req.user.uid,
                    action: JSON.stringify(action),
                    flow_version_id: newVersion.id
                });
            }

            // Limpiar archivos temporales
            logger.debug(`[CHUNKING] Limpiando archivos temporales...`);
            this.cleanupTempFiles(tempDir);

            // Obtener el idioma del usuario para la auditoría
            const userLocale = this.getUserLocale(req);

            // Registrar restauración de flujo en auditoría
            auditService.registrarEdicion(
                req.user.name,
                EntityTypes.FLUJO.name,
                flow.id.toString(),
                flow.name,
                auditTranslations.getPropertyTranslation(EntityTypes.FLUJO.name, 'restauracion', null, userLocale),
                auditTranslations.formatValue(EntityTypes.FLUJO.name, 'version_restaurada', version.number, null, userLocale),
                auditTranslations.formatValue(EntityTypes.FLUJO.name, 'version_anterior', flow.ActiveStagingVersion ? flow.ActiveStagingVersion.number : 'N/A', null, userLocale),
                null,
                userLocale
            ).catch(error => {
                logger.error(`Error al registrar auditoría de restauración de flujo: ${error}`);
            });

            // Actualizar caché
            let flowIdToUpdateCache = flow.id;
            let cacheKeys = [];
            if (flow.master_flow_id !== null) {
                flowIdToUpdateCache = flow.master_flow_id;
            }

            cacheKeys.push(`${client}-${flowIdToUpdateCache}-staging`);
            cacheKeys.push(`${client}-${flowIdToUpdateCache}-tester`);
            cacheKeys.forEach(cacheKey => {
                if (redis !== null) {
                    redis.sessionClient.publish(client, JSON.stringify({
                        action: 'saving',
                        flow: flowIdToUpdateCache,
                        cacheKey: cacheKey
                    }), function (error) {
                        if (error !== null) {
                            logger.info(`Hubo un error al querer publicar en redis el flow ${flowIdToUpdateCache}-${flow.name}: ${error}`);
                        }
                        else {
                            logger.info(`Se publicó correctamente en redis el flow ${flowIdToUpdateCache}-${flow.name}`);
                        }
                    });
                } else if (myCache.has(cacheKey)) {
                    myCache.del(cacheKey);
                }
            });

            // Preparar la respuesta
            const responseData = {
                success: true,
                message: 'Flow restored',
                id: flow.id,
                versionId: newVersion.id,
                versionNumber: newVersion.number,
                data: flowContent
            };

            return res.status(200).send(responseData);
        } catch (error) {
            logger.error(`[ERROR] Error al fusionar fragmentos: ${error}`);

            // Intentar limpiar archivos temporales en caso de error
            try {
                this.cleanupTempFiles(tempDir);
            } catch (cleanupError) {
                logger.error(`[ERROR] Error al limpiar archivos temporales: ${cleanupError}`);
            }

            return res.status(500).send({ success: false, message: 'Error al fusionar fragmentos' });
        }
    }

    async restoreFlow(req, res) {
        if (req.query.flow_version_id == null) {
            return res.status(400).send({ success: false, message: 'id or flow_version_id is missing' });
        }

        var flow = await this.checkIfFlowExists(req, res);
        if (!flow) {
            return res.status(404).send({ success: false, message: 'Flow NOT FOUND' });
        }

        let version = await FlowVersion.findById(req.query.flow_version_id)

        if (!version) {
            return res.status(404).send({ success: false, message: 'Flow or version NOT FOUND' });
        }

        // Verificar si la solicitud es para carga en fragmentos
        if (req.body && req.body.isChunked) {
            logger.debug(`[CHUNKING] Recibida solicitud para restauración en fragmentos`);
            return res.status(200).send({
                success: true,
                message: 'Listo para recibir fragmentos',
                uploadId: req.body.uploadId,
                flowId: flow.id,
                versionId: version.id
            });
        }

        var newVersion = await FlowVersion.createNewVersion(version.blob, flow.id, null, req.user.uid, req.user.cid);
        await Flow.update({ ActiveStagingVersionId: newVersion.id }, { where: { id: req.query.id } });

        if (flow.ActiveStagingVersionId) {
            // only log the restore if we've a staging version
            let action = {
                t: DiffHelper.changeType.FLOW_RESTORED,
                v: version.number
            };
            await UserAction.create({
                user_id: req.user.uid,
                action: JSON.stringify(action),
                flow_version_id: newVersion.id
            });

            // Obtener el idioma del usuario para la auditoría
            const userLocale = this.getUserLocale(req);

            // Registrar restauración de flujo en auditoría
            auditService.registrarEdicion(
                req.user.name,
                EntityTypes.FLUJO.name,
                flow.id.toString(),
                flow.name,
                auditTranslations.getPropertyTranslation(EntityTypes.FLUJO.name, 'restauracion', null, userLocale),
                auditTranslations.formatValue(EntityTypes.FLUJO.name, 'version_restaurada', version.number, null, userLocale),
                auditTranslations.formatValue(EntityTypes.FLUJO.name, 'version_anterior', flow.ActiveStagingVersion ? flow.ActiveStagingVersion.number : 'N/A', null, userLocale),
                null,
                userLocale
            ).catch(error => {
                logger.error(`Error al registrar auditoría de restauración de flujo: ${error}`);
            });
        }

        let flowIdToUpdateCache;
        let cacheKeys = [];
        if (flow.master_flow_id !== null) {
            flowIdToUpdateCache = flow.master_flow_id;
        } else {
            flowIdToUpdateCache = flow.id;
        }

        cacheKeys.push(`${client}-${flowIdToUpdateCache}-staging`);
        cacheKeys.push(`${client}-${flowIdToUpdateCache}-tester`);
        cacheKeys.forEach(cacheKey => {
            if (redis !== null) {
                redis.sessionClient.publish(client, JSON.stringify({
                    action: 'saving',
                    flow: flowIdToUpdateCache,
                    cacheKey: cacheKey
                }), function (error) {
                    if (error !== null) {
                        logger.info(`Hubo un error al querer publicar en redis el flow ${flowIdToUpdateCache}-${flow.name}: ${error}`);
                    }
                    else {
                        logger.info(`Se publicó correctamente en redis el flow ${flowIdToUpdateCache}-${flow.name}`);
                    }
                });
            } else if (myCache.has(cacheKey)) {
                flow = myCache.del(cacheKey);
            }
        });

        req.params.id = req.query.id;
        flow = await this.getFlow(req, res, false);

        // Verificar si el cliente acepta respuestas comprimidas
        const acceptEncoding = req.headers['x-accept-zip'] === 'true';

        // Preparar la respuesta
        const responseData = {
            success: true,
            message: 'Flow updated',
            data: flow.ActiveStagingVersion.blob
        };

        // Si el cliente acepta zip y los datos son grandes (más de 1MB)
        if (acceptEncoding && responseData.data.length > 1024 * 1024) {
            const dataSizeKB = Math.round(responseData.data.length / 1024);
            logger.debug(`[COMPRESIÓN] Comprimiendo respuesta grande de ${dataSizeKB} KB`);
            const JSZip = require('jszip');

            try {
                // Comprimir con JSZip
                const zip = new JSZip();
                zip.file('data.json', JSON.stringify(responseData));

                const content = await zip.generateAsync({
                    type: 'nodebuffer',
                    compression: 'DEFLATE',
                    compressionOptions: {
                        level: 7
                    }
                });

                const compressedSizeKB = Math.round(content.length / 1024);
                const compressionRatio = ((1 - (content.length / responseData.data.length)) * 100).toFixed(2);
                logger.debug(`[COMPRESIÓN] Respuesta comprimida: ${compressedSizeKB} KB. Ratio: ${compressionRatio}%`);

                // Enviar respuesta comprimida
                res.setHeader('Content-Type', 'application/zip');
                res.setHeader('X-Content-Encoding', 'zip');
                return res.send(content);
            } catch (error) {
                logger.error(`Error al comprimir respuesta: ${error}`);
                // Si hay error en la compresión, enviar sin comprimir
                return res.status(200).send(responseData);
            }
        } else {
            // Enviar respuesta sin comprimir
            return res.status(200).send(responseData);
        }
    }

    /**
     * Maneja la recepción de un fragmento de flujo para importación
     * @param {Object} req Solicitud HTTP
     * @param {Object} res Respuesta HTTP
     * @returns {Object} Respuesta HTTP
     */
    async overrideVersionChunk(req, res) {
        // Verificar que exista el flujo
        var flow = await this.checkIfFlowExists(req, res, false);
        if (!flow) {
            return res.status(404).send({ success: false, message: 'Flow NOT FOUND' });
        }

        // Verificar que exista la versión
        var version = await FlowVersion.findOne({
            where: {
                id: req.params.versionId
            }
        });
        if (!version) {
            return res.status(404).send({ success: false, message: 'Version NOT FOUND' });
        }

        // Obtener datos del fragmento
        const { uploadId, hash, totalChunks, chunk } = req.body;

        if (!uploadId || hash === undefined || !totalChunks || !chunk) {
            return res.status(400).send({ success: false, message: 'Faltan parámetros requeridos' });
        }

        try {
            // Crear directorio temporal si no existe
            const tempDir = `${contextFolder}/temp/${uploadId}`;
            if (!fs.existsSync(tempDir)) {
                fs.mkdirSync(tempDir, { recursive: true });
            }

            // Guardar el fragmento en un archivo temporal
            const chunkPath = `${tempDir}/${hash}`;
            fs.writeFileSync(chunkPath, chunk);

            logger.debug(`[CHUNKING] Fragmento ${hash + 1}/${totalChunks} recibido y guardado en ${chunkPath}`);

            return res.status(200).send({
                success: true,
                message: `Fragmento ${hash + 1}/${totalChunks} recibido correctamente`,
                hash: hash,
                totalChunks: totalChunks
            });
        } catch (error) {
            logger.error(`[ERROR] Error al procesar fragmento: ${error}`);
            return res.status(500).send({ success: false, message: 'Error al procesar fragmento' });
        }
    }

    /**
     * Fusiona todos los fragmentos de un flujo y lo guarda en la versión correspondiente
     * @param {Object} req Solicitud HTTP
     * @param {Object} res Respuesta HTTP
     * @returns {Object} Respuesta HTTP
     */
    async overrideVersionMerge(req, res) {
        // Verificar que exista el flujo
        var flow = await this.checkIfFlowExists(req, res, false);
        if (!flow) {
            return res.status(404).send({ success: false, message: 'Flow NOT FOUND' });
        }

        // Verificar que exista la versión
        var version = await FlowVersion.findOne({
            where: {
                id: req.params.versionId
            }
        });
        if (!version) {
            return res.status(404).send({ success: false, message: 'Version NOT FOUND' });
        }

        // Obtener datos de la solicitud
        const { uploadId, totalChunks } = req.body;

        if (!uploadId || !totalChunks) {
            return res.status(400).send({ success: false, message: 'Faltan parámetros requeridos' });
        }

        // Directorio temporal para los fragmentos
        const tempDir = `${contextFolder}/temp/${uploadId}`;
        const combinedFilePath = `${tempDir}/combined.zip`;
        const decompressedFilePath = `${tempDir}/decompressed.json`;

        try {
            // Verificar que todos los fragmentos estén presentes
            let allChunksPresent = true;
            for (let i = 0; i < totalChunks; i++) {
                const chunkPath = `${tempDir}/${i}`;
                if (!fs.existsSync(chunkPath)) {
                    allChunksPresent = false;
                    logger.error(`[ERROR] Falta el fragmento ${i + 1}/${totalChunks}`);
                    break;
                }
            }

            if (!allChunksPresent) {
                return res.status(400).send({ success: false, message: 'Faltan fragmentos para completar la carga' });
            }

            logger.debug(`[CHUNKING] Iniciando fusión de ${totalChunks} fragmentos...`);

            // Crear un stream de escritura para el archivo combinado
            const writeStream = fs.createWriteStream(combinedFilePath);

            // Procesar cada chunk como stream secuencialmente para evitar sobrecarga de memoria
            for (let i = 0; i < totalChunks; i++) {
                const chunkPath = `${tempDir}/${i}`;

                // Usar promesas para manejar el streaming de manera asíncrona
                await new Promise((resolve, reject) => {
                    const chunkStream = fs.createReadStream(chunkPath);

                    chunkStream.on('error', (error) => {
                        logger.error(`[ERROR] Error al leer fragmento ${i}: ${error}`);
                        reject(error);
                    });

                    // Pipe el contenido al stream combinado, pero no cerrar el writeStream todavía
                    chunkStream.pipe(writeStream, { end: false });

                    // Cuando termine este chunk, resolver la promesa
                    chunkStream.on('end', () => {
                        resolve();
                    });
                });
            }

            // Cerrar el stream de escritura y esperar a que termine
            await new Promise((resolve, reject) => {
                writeStream.end();
                writeStream.on('finish', () => {
                    resolve();
                });
                writeStream.on('error', reject);
            });

            // Obtener estadísticas del archivo combinado
            const stats = fs.statSync(combinedFilePath);
            logger.debug(`[CHUNKING] Tamaño del archivo combinado: ${Math.round(stats.size / 1024)} KB`);

            // Descomprimir el archivo combinado
            logger.debug(`[DESCOMPRESIÓN] Iniciando descompresión del archivo combinado...`);

            // Leer el archivo combinado como buffer
            const combinedBuffer = fs.readFileSync(combinedFilePath);

            let decompressedContent = null;

            try {
                // Intentar múltiples métodos de descompresión en secuencia
                const base64Start = combinedBuffer.toString('ascii', 0, 10);

                // 1. Intentar descomprimir con zlib directamente (formato gzip)
                try {
                    decompressedContent = zlib.unzipSync(combinedBuffer).toString('utf8');
                    logger.debug(`[DESCOMPRESIÓN] Descompresión con zlib exitosa`);
                } catch (gzipError) {
                    // 2. Verificar si podría ser base64 de gzip (comienza con H4sI)
                    if (base64Start.startsWith('H4sI')) {
                        try {
                            const binaryData = Buffer.from(combinedBuffer.toString('ascii'), 'base64');
                            decompressedContent = zlib.unzipSync(binaryData).toString('utf8');
                            logger.debug(`[DESCOMPRESIÓN] Descompresión de base64+gzip exitosa`);
                        } catch (base64GzipError) {
                            // Continuar con el siguiente método
                        }
                    }

                    // 2.1 Verificar si podría ser base64 de ZIP (comienza con UEsD)
                    if (!decompressedContent && (base64Start.startsWith('UEsD') || base64Start.startsWith('UEsDB'))) {
                        try {
                            const binaryData = Buffer.from(combinedBuffer.toString('ascii'), 'base64');
                            const JSZip = require('jszip');
                            const zip = await JSZip.loadAsync(binaryData);

                            // Obtener el primer archivo
                            const files = Object.keys(zip.files);
                            if (files.length === 0) {
                                throw new Error('Archivo ZIP vacío');
                            }

                            // Extraer el contenido
                            decompressedContent = await zip.files[files[0]].async('string');
                            logger.debug(`[DESCOMPRESIÓN] Descompresión de base64+ZIP exitosa`);
                        } catch (base64ZipError) {
                            // Continuar con el siguiente método
                        }
                    }

                    // 3. Si aún no se ha descomprimido, intentar con JSZip
                    if (!decompressedContent) {
                        try {
                            const JSZip = require('jszip');
                            const zip = await JSZip.loadAsync(combinedBuffer);

                            // Obtener el primer archivo
                            const files = Object.keys(zip.files);
                            if (files.length === 0) {
                                throw new Error('Archivo ZIP vacío');
                            }

                            // Extraer el contenido
                            decompressedContent = await zip.files[files[0]].async('string');
                            logger.debug(`[DESCOMPRESIÓN] Descompresión con JSZip exitosa`);
                        } catch (zipError) {
                            // Si llegamos aquí, ningún método funcionó
                            throw new Error(`No se pudo descomprimir el archivo con ningún método: ${zipError.message}`);
                        }
                    }
                }

                // 4. Verificar que el contenido se haya descomprimido correctamente
                if (!decompressedContent) {
                    throw new Error('No se pudo descomprimir el contenido con ningún método');
                }

                // Guardar el contenido descomprimido en un archivo temporal
                fs.writeFileSync(decompressedFilePath, decompressedContent);
                logger.debug(`[DESCOMPRESIÓN] Contenido descomprimido y guardado en archivo temporal`);
            } catch (error) {
                logger.error(`[ERROR] Error al descomprimir datos: ${error}`);
                throw error;
            }

            // Leer el contenido del archivo temporal
            const flowContent = fs.readFileSync(decompressedFilePath, 'utf8');

            // Validar que el contenido sea un JSON válido
            try {
                JSON.parse(flowContent);
                logger.debug(`[VALIDACIÓN] JSON validado correctamente para importación`);
            } catch (jsonError) {
                logger.error(`[ERROR] Error al parsear JSON: ${jsonError.message}`);
                logger.error(`[ERROR] Primeros 200 caracteres del contenido: ${flowContent.substring(0, 200)}`);

                // Limpiar archivos temporales
                this.cleanupTempFiles(tempDir);

                return res.status(403).send({ success: false, message: 'El contenido del archivo es inválido' });
            }

            // Crear una nueva versión con el contenido
            logger.debug(`[CHUNKING] Creando nueva versión del flujo...`);
            var newVersion = await FlowVersion.createNewVersion(flowContent, flow.id, null, req.user.uid, req.user.cid);
            await Flow.update({ ActiveStagingVersionId: newVersion.id }, { where: { id: flow.id } });

            let action = {
                t: DiffHelper.changeType.FLOW_OVERRIDE,
                v: version.number
            };
            await UserAction.create({
                user_id: req.user.uid,
                action: JSON.stringify(action),
                flow_version_id: newVersion.id
            });

            // Limpiar archivos temporales
            logger.debug(`[CHUNKING] Limpiando archivos temporales...`);
            this.cleanupTempFiles(tempDir);

            // Registrar la acción en auditoría
            const userLocale = this.getUserLocale(req);
            auditService.registrarEdicion(
                req.user.name,
                EntityTypes.FLUJO.name,
                flow.id.toString(),
                flow.name,
                auditTranslations.getPropertyTranslation(EntityTypes.FLUJO.name, 'sobrescritura', null, userLocale),
                auditTranslations.formatValue(EntityTypes.FLUJO.name, 'version_sobrescrita', version.number, null, userLocale),
                null,
                null,
                userLocale
            ).catch(error => {
                logger.error(`Error al registrar auditoría de sobrescritura de versión: ${error}`);
            });

            return res.status(200).send({
                success: true,
                message: 'Versión actualizada correctamente',
                flowId: flow.id,
                versionId: newVersion.id,
                versionNumber: newVersion.number
            });
        } catch (error) {
            logger.error(`[ERROR] Error al fusionar fragmentos: ${error}`);

            // Intentar limpiar archivos temporales en caso de error
            try {
                this.cleanupTempFiles(tempDir);
            } catch (cleanupError) {
                logger.error(`[ERROR] Error al limpiar archivos temporales: ${cleanupError}`);
            }

            return res.status(500).send({ success: false, message: 'Error al fusionar fragmentos' });
        }
    }

    async overrideFlowVersion(req, res) {
        if (req.params.flow_version_id == null) {
            return res.status(400).send({ success: false, message: 'id or flow_version_id is missing' });
        }

        // Verificar si los datos están comprimidos
        let blobToSave = null;

        // Verificar qué datos tenemos en la solicitud
        logger.debug(`[DEBUG] Headers recibidos:`, JSON.stringify(req.headers));
        logger.debug(`[DEBUG] Propiedades en req.body:`, Object.keys(req.body));

        // Verificar si hay datos comprimidos
        const hasZipHeader = req.headers['x-content-encoding'] === 'zip';
        const hasZipData = req.body.zipData !== undefined;

        logger.debug(`[DEBUG] ¿Tiene encabezado de compresión?`, hasZipHeader);
        logger.debug(`[DEBUG] ¿Tiene datos comprimidos?`, hasZipData);

        if (hasZipHeader && hasZipData) {
            // Calcular tamaño comprimido en KB
            const compressedSizeKB = Math.round((req.body.zipData.length * 0.75) / 1024); // Aproximación base64 a binario
            logger.debug(`[DESCOMPRESIÓN] Iniciando descompresión de flujo para importación. Tamaño comprimido: ${compressedSizeKB} KB`);

            const JSZip = require('jszip');
            try {
                // Descomprimir los datos
                const zip = await JSZip.loadAsync(Buffer.from(req.body.zipData, 'base64'));

                // Obtener el primer archivo
                const files = Object.keys(zip.files);
                logger.debug(`[DEBUG] Archivos encontrados en ZIP:`, files);

                if (files.length === 0) {
                    return res.status(400).send({ success: false, message: 'Archivo ZIP vacío' });
                }

                // Leer el contenido del archivo
                const flowDataFile = files.find(f => f === 'flow_data.json') || files[0];
                logger.debug(`[DEBUG] Usando archivo "${flowDataFile}" del ZIP`);
                blobToSave = await zip.files[flowDataFile].async('string');
                logger.debug(`[DEBUG] Primeros 100 caracteres del contenido descomprimido:`, blobToSave.substring(0, 100));

                // Calcular tamaño descomprimido en KB
                const decompressedSizeKB = Math.round(blobToSave.length / 1024);
                const compressionRatio = Math.round((1 - (compressedSizeKB / decompressedSizeKB)) * 100);
                logger.debug(`[DESCOMPRESIÓN] Descompresión completada para importación. Tamaño descomprimido: ${decompressedSizeKB} KB. Ratio: ${compressionRatio}%`);
            } catch (error) {
                logger.error(`[ERROR] Error al descomprimir datos: ${error}`);
                return res.status(400).send({ success: false, message: 'Error al descomprimir datos' });
            }
        } else {
            logger.debug(`[DEBUG] Usando datos sin comprimir. ¿Tiene blob?`, req.body.blob ? 'Sí' : 'No');
            if (req.body.blob) {
                logger.debug(`[DEBUG] Tamaño del blob sin comprimir: ${Math.round(req.body.blob.length / 1024)} KB`);
                logger.debug(`[DEBUG] Primeros 100 caracteres del blob:`, req.body.blob.substring(0, 100));
            }
            blobToSave = req.body.blob;
        }

        try {
            // Verificar que blobToSave no sea null o undefined antes de intentar parsearlo
            if (!blobToSave) {
                logger.error('[ERROR] El blob a guardar es null o undefined');
                return res.status(403).send({ success: false, message: 'El contenido del archivo es inválido' });
            }

            // Intentar parsear el JSON
            try {
                JSON.parse(blobToSave);
                logger.debug(`[VALIDACIÓN] JSON validado correctamente para importación`);
            } catch (jsonError) {
                logger.error(`[ERROR] Error al parsear JSON: ${jsonError.message}`);
                logger.error(`[ERROR] Primeros 200 caracteres del blob: ${blobToSave.substring(0, 200)}`);
                return res.status(403).send({ success: false, message: 'El contenido del archivo es inválido' });
            }
        }
        catch (ex) {
            logger.error(`[ERROR] Error general al procesar el blob: ${ex.message}`);
            return res.status(403).send({ success: false, message: 'El contenido del archivo es inválido' });
        }

        var flow = await this.checkIfFlowExists(req, res, false);
        if (!flow) {
            return res.status(404).send({ success: false, message: 'Flow NOT FOUND' });
        }

        var version = await FlowVersion.findOne({
            where: {
                id: req.params.flow_version_id
            }
        });
        if (!version) {
            return res.status(404).send({ success: false, message: 'Flow or version NOT FOUND' });
        }

        var newVersion = await FlowVersion.createNewVersion(blobToSave, flow.id, null, req.user.uid, req.user.cid);
        await Flow.update({ ActiveStagingVersionId: newVersion.id }, { where: { id: flow.id } });

        let action = {
            t: DiffHelper.changeType.FLOW_OVERRIDE,
            v: version.number
        };
        await UserAction.create({
            user_id: req.user.uid,
            action: JSON.stringify(action),
            flow_version_id: newVersion.id
        });

        // Obtener el idioma del usuario para la auditoría
        const userLocale = req.session && req.session.locale ? req.session.locale :
            req.cookies && req.cookies.locale ? req.cookies.locale :
                req.headers['accept-language'] ? req.headers['accept-language'].split(',')[0].substring(0, 2) : 'es';

        // Obtener la versión anterior (si existe)
        const previousVersion = await FlowVersion.findOne({
            where: {
                flow_id: flow.id,
                id: { [Op.ne]: version.id },
                number: { [Op.lt]: version.number }
            },
            order: [['number', 'DESC']]
        });

        // Registrar sobrescritura de versión de flujo en auditoría
        auditService.registrarEdicion(
            req.user.name,
            EntityTypes.FLUJO.name,
            flow.id.toString(),
            flow.name,
            auditTranslations.getPropertyTranslation(EntityTypes.FLUJO.name, 'sobrescritura', null, userLocale),
            auditTranslations.formatValue(EntityTypes.FLUJO.name, 'version_sobrescrita', version.number, null, userLocale),
            auditTranslations.formatValue(EntityTypes.FLUJO.name, 'version_anterior', previousVersion ? previousVersion.number : 'N/A', null, userLocale),
            null,
            userLocale
        ).catch(error => {
            logger.error(`Error al registrar auditoría de sobrescritura de versión de flujo: ${error}`);
        });

        let flowIdToUpdateCache = flow.id;
        let cacheKeys = [];
        if (flow.master_flow_id !== null) {
            flowIdToUpdateCache = flow.master_flow_id;
        }

        cacheKeys.push(`${client}-${flowIdToUpdateCache}-staging`);
        cacheKeys.push(`${client}-${flowIdToUpdateCache}-tester`);
        cacheKeys.forEach(cacheKey => {
            if (redis !== null) {
                redis.sessionClient.publish(client, JSON.stringify({
                    action: 'saving',
                    flow: flowIdToUpdateCache,
                    cacheKey: cacheKey
                }), function (error) {
                    if (error !== null) {
                        logger.info(`Hubo un error al querer publicar en redis el flow ${flowIdToUpdateCache}-${flow.name}: ${error}`);
                    }
                    else {
                        logger.info(`Se publicó correctamente en redis el flow ${flowIdToUpdateCache}-${flow.name}`);
                    }
                });
            } else if (myCache.has(cacheKey)) {
                flow = myCache.del(cacheKey);
            }
        });

        return res.status(200).send({ success: true, message: 'Flow updated' });
    }

    async deleteFlow(req, res) {
        var flow = await this.checkIfFlowExists(req, res);
        if (!flow) {
            return res.status(404).send({ success: false, message: 'Flow NOT FOUND' });
        }

        if (!standAlone) {
            logger.info(`Se invocará a ySocial para verificar si el flow ${flow.id}-${flow.name} es usado`);

            let url = process.env.ySocialUrl;
            try {
                let flowDefinition = JSON.parse(flow.ActiveStagingVersion.blob);
                if (typeof (flowDefinition.YSocialSettings) !== 'undefined' &&
                    flowDefinition.YSocialSettings !== null &&
                    typeof (flowDefinition.YSocialSettings.Url) !== 'undefined' &&
                    flowDefinition.YSocialSettings.Url !== null) {
                    url = flowDefinition.YSocialSettings.Url;
                }
            }
            catch (e) {
                logger.error(`No se pudo leer la configuración de ySocial del flow: ${e}`);
            }

            if (!url.endsWith('/')) {
                url += '/';
            }

            url += 'services/flow/isused?id=' + flow.id;

            try {
                let requestOptionsCA;
                if (fs.existsSync('ssl/request_ca_certs.pem')) {
                    requestOptionsCA = [fs.readFileSync('ssl/request_ca_certs.pem')];
                    logger.info(`Se utilizará los certificados certificantes desde ssl/request_ca_certs.pem`);
                }

                const httpsAgent = new https.Agent({
                    rejectUnauthorized: false,
                    ca: requestOptionsCA
                });

                var result = await axios.get(url, {
                    headers: authorization('GET', url),
                    httpsAgent: httpsAgent
                });

                if (result.data.Success && result.data.Result.IsUsed) {
                    return res.status(403).send({
                        success: false,
                        message: 'Flow cannot be deleted because it\'s being used in ySocial',
                        code: 1000
                    });
                }
            }
            catch (e) {
                logger.error(`Se produjo un error invocando a ySocial para informar de la publicación del flow ${e}`);
                return res.status(403).send({
                    success: false,
                    message: 'Flow cannot be deleted because ySocial returned an error',
                    code: 1001
                });
            }
        }

        // Capturar información del flujo antes de eliminarlo para la auditoría
        const flowData = {
            id: flow.id,
            name: flow.name,
            isMaster: flow.master_flow_id === null
        };

        if (flow.master_flow_id === null) {
            let flows = await Flow.findAllModulesById(flow.id, flow.company_id, req.user.id)
            for (const f of flows) {
                await f.destroy();
            }
        }

        await flow.destroy();

        // Obtener el idioma del usuario
        const userLocale = req.session && req.session.locale ? req.session.locale :
            req.cookies && req.cookies.locale ? req.cookies.locale :
                req.headers['accept-language'] ? req.headers['accept-language'].split(',')[0].substring(0, 2) : 'es';

        // Registrar eliminación de flujo en auditoría
        auditService.registrarEliminacion(
            req.user.name,
            EntityTypes.FLUJO.name,
            flowData.id.toString(),
            flowData.name,
            null,
            userLocale
        ).catch(error => {
            logger.error(`Error al registrar auditoría de eliminación de flujo: ${error}`);
        });

        let flowIdToUpdateCache;
        if (flow.master_flow_id !== null) {
            flowIdToUpdateCache = flow.master_flow_id;
        } else {
            flowIdToUpdateCache = flow.id;
        }

        let cacheKey = `${client}-${flowIdToUpdateCache}-prod`;
        if (myCache.has(cacheKey)) {
            myCache.del(cacheKey);
        }
        cacheKey = `${client}-${flowIdToUpdateCache}-staging`;
        if (myCache.has(cacheKey)) {
            myCache.del(cacheKey);
        }
        cacheKey = `${client}-${flowIdToUpdateCache}-tester`;
        if (myCache.has(cacheKey)) {
            myCache.del(cacheKey);
        }

        return res.status(200).send({ success: true, message: 'Flow deleted' });
    }

    async downloadFlow(req, res) {
        try {
            var flow = await this.checkIfFlowExists(req, res, false);
            if (!flow) {
                return res.status(404).send({ success: false, message: 'Flow NOT FOUND' });
            }

            if (typeof (req.params.flow_version_id) === 'undefined') {
                req.params.flow_version_id = flow.ActiveProductionVersionId;
            }

            var version = await FlowVersion.findOne({
                where: {
                    id: req.params.flow_version_id
                }
            });
            if (!version) {
                return res.status(404).send({ success: false, message: 'Flow version NOT FOUND' });
            }

            // Verificar si el blob está comprimido (no comienza con '{')
            let blobContent = version.blob;
            if (blobContent && !blobContent.startsWith('{')) {
                try {
                    // Obtener los primeros caracteres para detectar el formato
                    const base64Start = blobContent.substring(0, 10);
                    const compressedData = Buffer.from(blobContent, 'base64');

                    // Intentar descomprimir según el formato detectado
                    if (base64Start.startsWith('H4sI')) {
                        // Formato gzip en base64
                        logger.info('[DESCOMPRESIÓN] Detectado formato gzip en base64');
                        blobContent = zlib.unzipSync(compressedData).toString();
                    } else if (base64Start.startsWith('UEsD') || base64Start.startsWith('UEsDB')) {
                        // Formato ZIP en base64
                        logger.info('[DESCOMPRESIÓN] Detectado formato ZIP en base64');
                        const JSZip = require('jszip');
                        const zip = await JSZip.loadAsync(compressedData);

                        // Obtener el primer archivo
                        const files = Object.keys(zip.files);
                        if (files.length === 0) {
                            throw new Error('Archivo ZIP vacío');
                        }

                        // Extraer el contenido
                        blobContent = await zip.files[files[0]].async('string');
                    } else {
                        // Intentar con gzip por defecto
                        logger.info('[DESCOMPRESIÓN] Formato no reconocido, intentando con gzip');
                        blobContent = zlib.unzipSync(compressedData).toString();
                    }

                    logger.info('[DESCOMPRESIÓN] Blob descomprimido correctamente para descarga');
                } catch (decompressError) {
                    logger.error(`Error al descomprimir blob: ${decompressError}`);
                    return res.status(500).send({ success: false, message: 'Error al descomprimir datos del flujo' });
                }
            }

            // Parsear el blob como JSON
            let parsedBlob;
            try {
                parsedBlob = JSON.parse(blobContent);
            } catch (parseError) {
                logger.error(`Error al parsear JSON del blob: ${parseError}`);
                logger.error(`Primeros 100 caracteres del blob: ${blobContent.substring(0, 100)}`);
                return res.status(500).send({ success: false, message: 'Error al parsear datos del flujo' });
            }

            let info = {
                id: flow.id,
                name: flow.name,
                channel: flow.channel,
                type: flow.type,
                def: parsedBlob
            };

            const jsonData = JSON.stringify(info);

            // Verificar si el cliente acepta respuestas comprimidas
            // Usar encabezado personalizado en lugar de accept-encoding para evitar restricciones del navegador
            const acceptZip = req.headers['x-accept-zip'] === 'true';

            // Si el cliente acepta zip y los datos son grandes (más de 800KB)
            if (acceptZip && jsonData.length > 800 * 1024) {
                // Calcular tamaño original en KB
                const originalSizeKB = Math.round(jsonData.length / 1024);
                logger.debug(`[COMPRESIÓN] Iniciando compresión de flujo para descarga. Tamaño original: ${originalSizeKB} KB`);

                const JSZip = require('jszip');

                // Comprimir con JSZip
                const zip = new JSZip();
                zip.file('flow.json', jsonData);

                const content = await zip.generateAsync({
                    type: 'nodebuffer',
                    compression: 'DEFLATE',
                    compressionOptions: {
                        level: 7
                    }
                });

                // Calcular tamaño comprimido en KB y ratio de compresión
                const compressedSizeKB = Math.round(content.length / 1024);
                const compressionRatio = ((1 - (content.length / jsonData.length)) * 100).toFixed(2);
                logger.debug(`[COMPRESIÓN] Compresión de flujo para descarga completada. Tamaño comprimido: ${compressedSizeKB} KB. Ratio: ${compressionRatio}%`);

                // Enviar respuesta comprimida
                res.setHeader('Content-Disposition', 'attachment; filename=' + flow.name + '_v' + version.number + '.zip');
                res.setHeader('Content-Type', 'application/zip');
                res.setHeader('X-Content-Encoding', 'zip');
                return res.send(content);
            } else {
                // Enviar respuesta sin comprimir
                res.setHeader('Content-Disposition', 'attachment; filename=' + flow.name + '_v' + version.number + '.json');
                res.setHeader('Content-Type', 'application/json; charset=utf-8');
                return res.send(jsonData);
            }
        } catch (error) {
            logger.error(`Error al descargar flujo: ${error}`);
            return res.status(500).send({ success: false, message: 'Error al descargar flujo' });
        }
    }

    async duplicateFlow(req, res) {
        var flow = await this.checkIfFlowExists(req, res);
        if (!flow) {
            return res.status(404).send({ success: false, message: 'Flow NOT FOUND' });
        }

        const t = await app.sequelize.transaction();
        try {
            var newFlow = await Flow.create({
                name: req.body.newName,
                user_id: req.user.uid,
                channel: flow.channel,
                company_id: req.user.cid,
                type: flow.type
            });
            var newStaginVersion = await FlowVersion.createNewVersion(flow.ActiveStagingVersion.blob, newFlow.id, null, req.user.uid, req.user.cid);
            await Flow.update({ ActiveStagingVersionId: newStaginVersion.id },
                {
                    where: { id: newFlow.id }
                });

            req.body.id = newFlow.id;
            var permissions = await this.addFlowPermissions(req);

            // Obtener el idioma del usuario
            const userLocale = this.getUserLocale(req);

            // Registrar duplicación de flujo en auditoría
            auditService.registrarCreacion(
                req.user.name,
                EntityTypes.FLUJO.name,
                newFlow.id.toString(),
                req.body.newName,
                auditTranslations.getValueTranslation('flow_duplicated'),
                null,
                userLocale
            ).catch(error => {
                logger.error(`Error al registrar auditoría de duplicación de flujo: ${error}`);
            });

            t.commit();
            res.status(201).send({
                success: true,
                message: 'Flow created',
                data: { id: flow.id, permissions: permissions }
            });
        }
        catch (error) {
            await t.rollback();
            logger.info(`Error en la creación de flujo, error: ${error}`);
            res.status(500).send({ success: false, message: 'error in create flows' });
        }
    }

    async getFlowConfiguration(req, res) {
        const { code, message, success, response } = await app.flowService.getFlowConfiguration(req);
        res.status(code).json({
            success,
            message,
            configuration: response.configuration,
            settings: response.settings
        });
    }

    async getTempFile(req, res) {
        await app.flowService.getTempFile(req, res);
    }

    /**
     * Maneja la recepción de un fragmento de flujo para importación
     * @param {Object} req Solicitud HTTP
     * @param {Object} res Respuesta HTTP
     * @returns {Object} Respuesta HTTP
     */
    async importFlowChunk(req, res) {
        // Obtener datos del fragmento
        const { uploadId, hash, totalChunks, chunk, flow_name, channel, type } = req.body;

        if (!uploadId || hash === undefined || !totalChunks || !chunk) {
            return res.status(400).send({ success: false, message: 'Faltan parámetros requeridos' });
        }

        try {
            // Crear directorio temporal si no existe
            const tempDir = `${contextFolder}/temp/${uploadId}`;
            if (!fs.existsSync(tempDir)) {
                fs.mkdirSync(tempDir, { recursive: true });

                // Guardar información del flujo en un archivo JSON
                const flowInfoPath = `${tempDir}/flow_info.json`;
                fs.writeFileSync(flowInfoPath, JSON.stringify({
                    flow_name: flow_name,
                    channel: channel,
                    type: type,
                    user_id: req.user.uid,
                    company_id: req.user.cid
                }));
            }

            // Guardar el fragmento en un archivo temporal
            const chunkPath = `${tempDir}/${hash}`;
            fs.writeFileSync(chunkPath, chunk);

            logger.debug(`[CHUNKING] Fragmento ${hash + 1}/${totalChunks} recibido y guardado en ${chunkPath}`);

            return res.status(200).send({
                success: true,
                message: `Fragmento ${hash + 1}/${totalChunks} recibido correctamente`,
                hash: hash,
                totalChunks: totalChunks
            });
        } catch (error) {
            logger.error(`[ERROR] Error al procesar fragmento: ${error}`);
            return res.status(500).send({ success: false, message: 'Error al procesar fragmento' });
        }
    }

    /**
     * Fusiona todos los fragmentos de un flujo y lo importa
     * @param {Object} req Solicitud HTTP
     * @param {Object} res Respuesta HTTP
     * @returns {Object} Respuesta HTTP
     */
    async importFlowMerge(req, res) {
        // Obtener datos de la solicitud
        const { uploadId, totalChunks } = req.body;

        if (!uploadId || !totalChunks) {
            return res.status(400).send({ success: false, message: 'Faltan parámetros requeridos' });
        }

        // Directorio temporal para los fragmentos
        const tempDir = `${contextFolder}/temp/${uploadId}`;
        const combinedFilePath = `${tempDir}/combined.zip`;
        const decompressedFilePath = `${tempDir}/decompressed.json`;

        try {
            // Verificar que exista la información del flujo
            const flowInfoPath = `${tempDir}/flow_info.json`;
            if (!fs.existsSync(flowInfoPath)) {
                return res.status(400).send({ success: false, message: 'Falta información del flujo' });
            }

            const flowInfo = JSON.parse(fs.readFileSync(flowInfoPath, 'utf8'));

            // Verificar que todos los fragmentos estén presentes
            let allChunksPresent = true;
            for (let i = 0; i < totalChunks; i++) {
                const chunkPath = `${tempDir}/${i}`;
                if (!fs.existsSync(chunkPath)) {
                    allChunksPresent = false;
                    logger.error(`[ERROR] Falta el fragmento ${i + 1}/${totalChunks}`);
                    break;
                }
            }

            if (!allChunksPresent) {
                return res.status(400).send({ success: false, message: 'Faltan fragmentos para completar la carga' });
            }

            logger.debug(`[CHUNKING] Iniciando fusión de ${totalChunks} fragmentos usando streams...`);

            // Crear un stream de escritura para el archivo combinado
            const writeStream = fs.createWriteStream(combinedFilePath);

            // Procesar cada chunk como stream secuencialmente para evitar sobrecarga de memoria
            for (let i = 0; i < totalChunks; i++) {
                const chunkPath = `${tempDir}/${i}`;

                // Usar promesas para manejar el streaming de manera asíncrona
                await new Promise((resolve, reject) => {
                    const chunkStream = fs.createReadStream(chunkPath);

                    chunkStream.on('error', (error) => {
                        logger.error(`[ERROR] Error al leer fragmento ${i}: ${error}`);
                        reject(error);
                    });

                    // Pipe el contenido al stream combinado, pero no cerrar el writeStream todavía
                    chunkStream.pipe(writeStream, { end: false });

                    // Cuando termine este chunk, resolver la promesa
                    chunkStream.on('end', () => {
                        resolve();
                    });
                });
            }

            // Cerrar el stream de escritura y esperar a que termine
            await new Promise((resolve, reject) => {
                writeStream.end();
                writeStream.on('finish', () => {
                    resolve();
                });
                writeStream.on('error', reject);
            });

            // Obtener estadísticas del archivo combinado
            const stats = fs.statSync(combinedFilePath);
            logger.debug(`[CHUNKING] Tamaño del archivo combinado: ${Math.round(stats.size / 1024)} KB`);

            // Descomprimir el archivo combinado
            logger.debug(`[DESCOMPRESIÓN] Iniciando descompresión del archivo combinado...`);

            // Leer el archivo combinado como buffer
            const combinedBuffer = fs.readFileSync(combinedFilePath);

            let decompressedContent = null;

            try {
                // Intentar múltiples métodos de descompresión en secuencia
                const base64Start = combinedBuffer.toString('ascii', 0, 10);

                // 1. Intentar descomprimir con zlib directamente (formato gzip)
                try {
                    decompressedContent = zlib.unzipSync(combinedBuffer).toString('utf8');
                    logger.debug(`[DESCOMPRESIÓN] Descompresión con zlib exitosa`);
                } catch (gzipError) {
                    // 2. Verificar si podría ser base64 de gzip (comienza con H4sI)
                    if (base64Start.startsWith('H4sI')) {
                        try {
                            const binaryData = Buffer.from(combinedBuffer.toString('ascii'), 'base64');
                            decompressedContent = zlib.unzipSync(binaryData).toString('utf8');
                            logger.debug(`[DESCOMPRESIÓN] Descompresión de base64+gzip exitosa`);
                        } catch (base64GzipError) {
                            // Continuar con el siguiente método
                        }
                    }

                    // 2.1 Verificar si podría ser base64 de ZIP (comienza con UEsD)
                    if (!decompressedContent && (base64Start.startsWith('UEsD') || base64Start.startsWith('UEsDB'))) {
                        try {
                            const binaryData = Buffer.from(combinedBuffer.toString('ascii'), 'base64');
                            const JSZip = require('jszip');
                            const zip = await JSZip.loadAsync(binaryData);

                            // Obtener el primer archivo
                            const files = Object.keys(zip.files);
                            if (files.length === 0) {
                                throw new Error('Archivo ZIP vacío');
                            }

                            // Extraer el contenido
                            decompressedContent = await zip.files[files[0]].async('string');
                            logger.debug(`[DESCOMPRESIÓN] Descompresión de base64+ZIP exitosa`);
                        } catch (base64ZipError) {
                            // Continuar con el siguiente método
                        }
                    }

                    // 3. Si aún no se ha descomprimido, intentar con JSZip
                    if (!decompressedContent) {
                        try {
                            const JSZip = require('jszip');
                            const zip = await JSZip.loadAsync(combinedBuffer);

                            // Obtener el primer archivo
                            const files = Object.keys(zip.files);
                            if (files.length === 0) {
                                throw new Error('Archivo ZIP vacío');
                            }

                            // Extraer el contenido
                            decompressedContent = await zip.files[files[0]].async('string');
                            logger.debug(`[DESCOMPRESIÓN] Descompresión con JSZip exitosa`);
                        } catch (zipError) {
                            // Si llegamos aquí, ningún método funcionó
                            throw new Error(`No se pudo descomprimir el archivo con ningún método: ${zipError.message}`);
                        }
                    }
                }

                // 4. Verificar que el contenido se haya descomprimido correctamente
                if (!decompressedContent) {
                    throw new Error('No se pudo descomprimir el contenido con ningún método');
                }

                // Guardar el contenido descomprimido en un archivo temporal
                fs.writeFileSync(decompressedFilePath, decompressedContent);
                logger.debug(`[DESCOMPRESIÓN] Contenido descomprimido y guardado en archivo temporal`);
            } catch (error) {
                logger.error(`[ERROR] Error al descomprimir datos: ${error}`);
                throw error;
            }

            // Leer el contenido del archivo temporal
            const flowContent = fs.readFileSync(decompressedFilePath, 'utf8');

            // Validar que el contenido sea un JSON válido
            try {
                JSON.parse(flowContent);
                logger.debug(`[VALIDACIÓN] JSON validado correctamente para importación`);
            } catch (jsonError) {
                logger.error(`[ERROR] Error al parsear JSON: ${jsonError.message}`);
                logger.error(`[ERROR] Primeros 200 caracteres del contenido: ${flowContent.substring(0, 200)}`);

                // Limpiar archivos temporales
                this.cleanupTempFiles(tempDir);

                return res.status(403).send({ success: false, message: 'El contenido del archivo es inválido' });
            }

            // Crear el flujo con el contenido
            logger.debug(`[CHUNKING] Creando nuevo flujo...`);

            // Verificar que el flujo no exista ya
            let flowExists = await Flow.findOne({ where: { name: flowInfo.flow_name } });
            if (flowExists) {
                if (flowExists.master_flow_id) {
                    let masterExists = await Flow.findOne({ where: { id: flowExists.master_flow_id } });
                    return res.status(500).send({
                        success: false,
                        message: localeServices.translate("MODULE_EXISTS", {
                            moduleName: flowExists.name,
                            masterName: masterExists.name
                        })
                    });
                }
                return res.status(500).send({
                    success: false,
                    message: localeServices.translate("MASTER_EXISTS", {
                        flowName: flowExists.name
                    })
                });
            }

            // Crear el nuevo flujo
            var newFlow = await Flow.create({
                name: flowInfo.flow_name,
                user_id: req.user.uid,
                channel: flowInfo.channel,
                type: flowInfo.type,
                company_id: req.user.cid
            });

            // Crear la versión inicial
            var version = await FlowVersion.createNewVersion(flowContent, newFlow.id, null, req.user.uid, req.user.cid);
            await Flow.update({ ActiveStagingVersionId: version.id }, { where: { id: newFlow.id } });

            // Limpiar la caché si es necesario
            let cacheKey = `${client}-${newFlow.id}-prod`;
            if (myCache.has(cacheKey)) {
                myCache.del(cacheKey);
            }
            cacheKey = `${client}-${newFlow.id}-staging`;
            if (myCache.has(cacheKey)) {
                myCache.del(cacheKey);
            }
            cacheKey = `${client}-${newFlow.id}-tester`;
            if (myCache.has(cacheKey)) {
                myCache.del(cacheKey);
            }

            // Limpiar archivos temporales
            logger.debug(`[CHUNKING] Limpiando archivos temporales...`);
            this.cleanupTempFiles(tempDir);

            // Obtener el idioma del usuario
            const userLocale = req.session && req.session.locale ? req.session.locale :
                req.cookies && req.cookies.locale ? req.cookies.locale :
                    req.headers['accept-language'] ? req.headers['accept-language'].split(',')[0].substring(0, 2) : 'es';

            // Registrar la acción en auditoría
            auditService.registrarCreacion(
                req.user.name,
                EntityTypes.FLUJO.name,
                newFlow.id.toString(),
                req.body.flow_name,
                null,
                userLocale
            ).catch(error => {
                logger.error(`Error al registrar auditoría de creación de flujo: ${error}`);
            });

            return res.status(201).send({
                success: true,
                message: 'Flujo importado correctamente',
                id: newFlow.id,
                versionId: version.id,
                versionNumber: version.number
            });
        } catch (error) {
            logger.error(`[ERROR] Error al fusionar fragmentos: ${error}`);

            // Intentar limpiar archivos temporales en caso de error
            try {
                this.cleanupTempFiles(tempDir);
            } catch (cleanupError) {
                logger.error(`[ERROR] Error al limpiar archivos temporales: ${cleanupError}`);
            }

            return res.status(500).send({ success: false, message: 'Error al fusionar fragmentos' });
        }
    }

    async importFlow(req, res) {
        try {
            // Verificar si es una solicitud de carga en fragmentos
            if (req.body.isChunked) {
                logger.debug(`[CHUNKING] Recibida solicitud para importación en fragmentos`);
                return res.status(200).send({
                    success: true,
                    message: 'Listo para recibir fragmentos',
                    uploadId: req.body.uploadId
                });
            }

            // Verificar si los datos están comprimidos
            let blob = null;

            if (req.headers['x-content-encoding'] === 'zip' && req.body.zipData) {
                // Calcular tamaño comprimido en KB
                const compressedSizeKB = Math.round((req.body.zipData.length * 0.75) / 1024); // Aproximación base64 a binario
                logger.debug(`[DESCOMPRESIÓN] Iniciando descompresión de flujo importado. Tamaño comprimido: ${compressedSizeKB} KB`);

                const JSZip = require('jszip');
                try {
                    // Descomprimir los datos
                    const zip = await JSZip.loadAsync(Buffer.from(req.body.zipData, 'base64'));

                    // Obtener el primer archivo
                    const files = Object.keys(zip.files);
                    if (files.length === 0) {
                        return res.status(400).send({ success: false, message: 'Archivo ZIP vacío' });
                    }

                    // Leer el contenido del archivo
                    blob = await zip.files[files[0]].async('string');

                    // Calcular tamaño descomprimido en KB
                    const decompressedSizeKB = Math.round(blob.length / 1024);
                    const compressionRatio = Math.round((1 - (compressedSizeKB / decompressedSizeKB)) * 100);
                    logger.debug(`[DESCOMPRESIÓN] Descompresión de flujo importado completada. Tamaño descomprimido: ${decompressedSizeKB} KB. Ratio: ${compressionRatio}%`);
                } catch (error) {
                    logger.error(`Error al descomprimir datos: ${error}`);
                    return res.status(400).send({ success: false, message: 'Error al descomprimir datos' });
                }
            } else {
                blob = req.body.blob;
            }

            // Verificar que tenemos todos los datos necesarios
            if (req.body.flow_name == null || req.body.channel == null || blob == null || req.body.type == null) {
                return res.status(400).send({ success: false, message: 'flow_name, blob o channel están faltando' });
            }

            var newFlow = await Flow.create({
                name: req.body.flow_name,
                user_id: req.user.uid,
                channel: req.body.channel,
                type: req.body.type,
                company_id: req.user.cid
            });

            var version = await FlowVersion.createNewVersion(blob, newFlow.id, null, req.user.uid, req.user.cid);
            await Flow.update({ ActiveStagingVersionId: version.id },
                {
                    where: { id: newFlow.id }
                });

            let cacheKey = `${client}-${newFlow.id}-prod`;
            if (myCache.has(cacheKey)) {
                myCache.del(cacheKey);
            }

            // Obtener el idioma del usuario
            const userLocale = req.session && req.session.locale ? req.session.locale :
                req.cookies && req.cookies.locale ? req.cookies.locale :
                    req.headers['accept-language'] ? req.headers['accept-language'].split(',')[0].substring(0, 2) : 'es';

            // Registrar importación de flujo en auditoría
            auditService.registrarCreacion(
                req.user.name,
                EntityTypes.FLUJO.name,
                newFlow.id.toString(),
                req.body.flow_name,
                null,
                userLocale
            ).catch(error => {
                logger.error(`Error al registrar auditoría de importación de flujo: ${error}`);
            });

            return res.status(201).send({ success: true, message: 'Flow created' });
        } catch (error) {
            logger.error(`Error al importar flujo: ${error}`);
            return res.status(500).send({ success: false, message: 'Error al importar flujo' });
        }
    }

    async shareLocation(req, res) {
        let executor = await axios.post(`${executorUrl}api/flows/shareLocation`, req.body)
        res.status(executor.status).send(executor.data)
    }

    /**
     * Limpia los archivos temporales de un directorio
     * @param {string} tempDir Directorio temporal a limpiar
     */
    cleanupTempFiles(tempDir) {
        try {
            if (fs.existsSync(tempDir)) {
                // Leer todos los archivos en el directorio
                const files = fs.readdirSync(tempDir);

                // Eliminar cada archivo
                for (const file of files) {
                    const filePath = `${tempDir}/${file}`;
                    fs.unlinkSync(filePath);
                }

                // Eliminar el directorio
                fs.rmdirSync(tempDir);
                logger.debug(`[CLEANUP] Directorio temporal eliminado: ${tempDir}`);
            }
        } catch (error) {
            logger.error(`error: ${error}`);
        }
    }

    async executeFlow(req, res) {
        const { code, message, success, response } = await app.flowService.execute(req);
        res.status(code).send({ message, success, response });
    }

    async getPermissions(req, res) {
        let id = req.params.id;
        let flow = await Flow.findOne({
            where: {
                id: id,
                company_id: req.user.cid
            },
            include: [
                { model: FlowVersion, as: 'ActiveStagingVersion' },
                { model: FlowVersion, as: 'ActiveProductionVersion' }
            ]
        });

        if (!flow) {
            return res.status(404).send({ success: false, message: 'Flow NOT FOUND' });
        }

        let permissions = await UserPermissionFlow.findAll({
            where: {
                flowId: flow.id
            },
            include: [
                {
                    model: User,
                    as: 'User',
                    attributes: {
                        exclude: ['access_token', 'can_validate_passwords', 'company_id', 'created_at', 'lang', 'password', 'updated_at']
                    },
                    where: {
                        company_id: req.user.cid,
                    }
                }
            ]
        });

        if (typeof (permissions) !== 'undefined') {
            let permissionsResult = permissions.filter((permission) => {
                if (permission.User !== null) {
                    return permission;
                }
            })
            return res.status(200).send({ success: true, data: permissionsResult });
        }
        else {
            return res.status(404).send({ success: false, message: 'Permissions not found!' });
        }
    }

    async setPermissions(req, res) {
        if (req.body.flow === null || typeof (req.body.flow) === 'undefined') {
            return res.status(404).send({ success: false, message: 'Invalid flow' });
        }
        if (req.body.user === null || typeof (req.body.user) === 'undefined') {
            return res.status(404).send({ success: false, message: 'Invalid user' });
        }

        let flowPermission = await UserPermissionFlow.findOne({
            flowId: req.body.flow,
            userId: req.body.user
        });

        // Obtener información del flujo y usuario para auditoría
        const flow = await Flow.findOne({
            attributes: ['name'],
            where: { id: req.body.flow }
        });

        const user = await User.findOne({
            attributes: ['name'],
            where: { id: req.body.user }
        });

        if (typeof (flowPermission) !== 'undefined' && flowPermission !== null) {
            // Guardar permisos anteriores para auditoría
            const permisosAnteriores = {
                editar: flowPermission.canEdit,
                publicar: flowPermission.canPublish,
                estadisticas: flowPermission.canSeeStatistics
            };

            await UserPermissionFlow.update({
                canEdit: req.body.canEdit,
                canPublish: req.body.canPublish,
                canSeeStatistics: req.body.canSeeStatistics
            }, {
                where: {
                    flowId: req.body.flow,
                    userId: req.body.user
                }
            });

            // Registrar cambio de permisos en auditoría
            if (flow && user) {
                // Obtener el idioma del usuario
                const userLocale = this.getUserLocale(req);

                auditService.registrarEdicion(
                    req.user.name,
                    EntityTypes.PERMISOS_FLUJO.name,
                    `${req.body.flow}_${req.body.user}`,
                    `${auditTranslations.getEntityTranslation(EntityTypes.PERMISOS_FLUJO.name, null, userLocale)}: ${user.name} - ${flow.name}`,
                    auditTranslations.getPropertyTranslation(EntityTypes.PERMISOS_FLUJO.name, 'permisos', null, userLocale),
                    auditTranslations.formatValue(EntityTypes.PERMISOS_FLUJO.name, 'permisos', {
                        editar: req.body.canEdit,
                        publicar: req.body.canPublish,
                        estadisticas: req.body.canSeeStatistics
                    }, null, userLocale),
                    auditTranslations.formatValue(EntityTypes.PERMISOS_FLUJO.name, 'permisos', permisosAnteriores, null, userLocale),
                    null,
                    userLocale
                ).catch(error => {
                    logger.error(`Error al registrar auditoría de cambio de permisos de flujo: ${error}`);
                });
            }
        } else {
            await UserPermissionFlow.create({
                flowId: req.body.flow,
                userId: req.body.user,
                canEdit: req.body.canEdit,
                canPublish: req.body.canPublish,
                canSeeStatistics: req.body.canSeeStatistics
            });

            // Registrar asignación inicial de permisos en auditoría
            if (flow && user) {
                // Obtener el idioma del usuario
                const userLocale = this.getUserLocale(req);

                auditService.registrarCreacion(
                    req.user.name,
                    EntityTypes.PERMISOS_FLUJO.name,
                    `${req.body.flow}_${req.body.user}`,
                    `${auditTranslations.getEntityTranslation(EntityTypes.PERMISOS_FLUJO.name, null, userLocale)}: ${user.name} - ${flow.name}`,
                    auditTranslations.getValueTranslation('permissions_assigned', null, userLocale),
                    null,
                    userLocale
                ).catch(error => {
                    logger.error(`Error al registrar auditoría de asignación de permisos de flujo: ${error}`);
                });
            }
        }
        return res.status(200).send({ success: true, message: 'Permissions updated' });
    }

    async getWhatsappHSMTemplates(req, res) {
        let url = req.query.ysocial;

        if (!url.endsWith('/')) {
            url += '/';
        }
        url += 'services/flow/whatsapp/hsms';

        try {
            let result = await axios.get(url, {
                headers: authorization('GET', url)
            });

            if (result.data.Success) {
                return res.status(200).send({
                    success: true,
                    data: result.data.Result
                });
            }
            else {
                return res.status(403).send({
                    success: false,
                    message: 'couldn\'t get whatsapp services',
                    code: 1001
                });
            }
        }
        catch (e) {
            logger.error(`Se produjo un error invocando a ySocial para informar de la publicación del flow ${e}`);
            return res.status(403).send({
                success: false,
                message: 'couldn\'t get whatsapp services',
                code: 1001
            });
        }
    }

    async getWhatsappCatalogs(req, res) {
        let url = req.query.ysocial;

        if (!url.endsWith('/')) {
            url += '/';
        }
        url += 'services/flow/whatsapp/catalogs';

        try {
            let result = await axios.get(url, {
                headers: authorization('GET', url)
            });

            if (result.data.Success) {
                return res.status(200).send({
                    success: true,
                    data: result.data.Result
                });
            }
            else {
                return res.status(403).send({
                    success: false,
                    message: 'couldn\'t get whatsapp services',
                    code: 1001
                });
            }
        }
        catch (e) {
            logger.error(`Se produjo un error invocando a ySocial para informar de la publicación del flow ${e}`);
            return res.status(403).send({
                success: false,
                message: 'couldn\'t get whatsapp services',
                code: 1001
            });
        }
    }

    async postServiceConfig(req, res) {

    }

    /*async getOpenGraphResolveUrl(req, res) {
        let url = req.query.url;
        if (typeof (url) !== 'string') {
            return res.status(400).send({
                success: false,
                message: 'invalid url',
                code: 1001
            });
        }

        try {
            let response = await axios({
                method: 'GET',
                url: url
            });

            const metadata = await metascraper({ html: response.data, url });

            return res.status(200).send({
                success: true,
                data: metadata
            });
        }
        catch (e) {
            return res.status(400).send({
                success: false,
                message: 'invalid url',
                code: 1001,
                error: e
            });
        }
    }*/
}

let flowsController = new FlowsController();

router.get('/', ErrorHandler(flowsController.getFlows, flowsController));
router.get('/whatsapphsmtemplates', ErrorHandler(flowsController.getWhatsappHSMTemplates, flowsController));
router.get('/whatsappcatalogs', ErrorHandler(flowsController.getWhatsappCatalogs, flowsController));
router.get('/get_flow_info/:id', ErrorHandler(flowsController.getFlowInfo, flowsController));
router.get('/get_flow_chunk/:id/:chunkIndex', ErrorHandler(flowsController.getFlowChunk, flowsController));
router.get('/:id', ErrorHandler(flowsController.getFlow, flowsController));
router.get('/by/:channel', ErrorHandler(flowsController.getFlowsBySocialService, flowsController));
router.get('/versions/:id/:page', ErrorHandler(flowsController.getVersions, flowsController));
router.get('/modules/:id', ErrorHandler(flowsController.getModules, flowsController));
router.get('/changelog', ErrorHandler(flowsController.getChangeLog, flowsController));
router.get('/permissions/:id', ErrorHandler(flowsController.getPermissions, flowsController));
router.put('/permissions', ErrorHandler(flowsController.setPermissions, flowsController));
router.post('/', ErrorHandler(flowsController.createFlow, flowsController));
router.put('/', ErrorHandler(flowsController.saveFlow, flowsController));
router.put('/publish/:id', ErrorHandler(flowsController.publishFlow, flowsController));
router.put('/override_version/:id/:flow_version_id', ErrorHandler(flowsController.overrideFlowVersion, flowsController));
router.post('/override_version_chunk/:id/:versionId', ErrorHandler(flowsController.overrideVersionChunk, flowsController));
router.post('/override_version_merge/:id/:versionId', ErrorHandler(flowsController.overrideVersionMerge, flowsController));
router.post('/create_flow_chunk/:id', ErrorHandler(flowsController.createFlowChunk, flowsController));
router.post('/create_flow_merge/:id', ErrorHandler(flowsController.createFlowMerge, flowsController));
router.post('/import_flow_chunk', ErrorHandler(flowsController.importFlowChunk, flowsController));
router.post('/import_flow_merge', ErrorHandler(flowsController.importFlowMerge, flowsController));
router.post('/restore_flow_chunk', ErrorHandler(flowsController.restoreFlowChunk, flowsController));
router.post('/restore_flow_merge', ErrorHandler(flowsController.restoreFlowMerge, flowsController));
router.put('/restore_version', ErrorHandler(flowsController.restoreFlow, flowsController));
router.delete('/:deleteProductive', ErrorHandler(flowsController.deleteFlow, flowsController));
router.get('/download/:id', ErrorHandler(flowsController.downloadFlow, flowsController));
router.get('/download/:id/:flow_version_id', ErrorHandler(flowsController.downloadFlow, flowsController));
router.post('/duplicate/:id', ErrorHandler(flowsController.duplicateFlow, flowsController));
router.post('/import', ErrorHandler(flowsController.importFlow, flowsController));

router.post('/execute', ErrorHandler(flowsController.executeFlow, flowsController));
router.get('/configuration/:id/servicetype/:service_type_id', ErrorHandler(flowsController.getFlowConfiguration, flowsController));
router.get('/tempfile/:id/:filename', ErrorHandler(flowsController.getTempFile, flowsController));
router.post('/sharelocation', ErrorHandler(flowsController.shareLocation, flowsController));

router.post('/serviceconfig', ErrorHandler(flowsController.postServiceConfig, flowsController));

module.exports = router;
module.exports.flowsController = flowsController;
